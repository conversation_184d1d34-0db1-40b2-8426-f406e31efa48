---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2021 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

require("Common/DataReader")
require("Logic/Data/DataModel")
require("Logic/Data/EventDataModel")

---@class DataMgr 負責串檔初始化
---author KK
---version 1.0
---since [ProjectBase] 0.1
---date 2021.12.10
DataMgr = {}
local this = DataMgr

--串檔 DebugLog 開關
this.m_IsOpenDataMgrDebugLog = true

---讀取普通串檔
---@type thread 
local _Thread_LoadingData

---讀取事件串檔
---@type thread 
local _Thread_LoadingEventData

---讀取串檔完成Callback
---@type function 
local _CallBack_LoadEnd  = nil

---讀取串檔進度 要做甚麼
---@type function 
local _ProgressFunc  = nil

---失敗訊息
---@type string 
local _ErrorMsg = nil

---初始化完成的 串檔 數量
local _InitializedDataCount = 0
---初始化完成的 EventData 數量
local _InitializedEventDataCount = 0

--- 是否顯示 idx
---@type boolean
this.m_IS_SHOW_IDX = false

local function ResetData(iData)
	iData.m_IsDataInitialized = false
	iData.m_DataCount = 0
	iData.m_NewDataCount = 0
	iData.m_StreamReadByteTimes = 0
end

---停止讀檔
---@param iDataTable table 要停止的資料表
---@param iIsNeedResetData boolean 是否需要重置資料
local function StopReadData(iDataTable, iIsNeedResetData)
	--停止相對應的 coroutine
	for key, value in pairs(iDataTable) do
		local _Currentcoroutine = nil
		if DataModel.Contains(value) then
			_Currentcoroutine = DataModel.m_DataCoroutineTable[value.m_ASSET_NAME]
		else
			_Currentcoroutine = EventDataModel.m_EventDataCoroutineTable[value.m_ASSET_NAME]
		end
		coroutine.stop(_Currentcoroutine)
		if iIsNeedResetData then
			ResetData(value)
		end
	end
end



---設定 
local function SetMgrAllCoroutineDead()
	if _Thread_LoadingData ~= nil then
		coroutine.stop(_Thread_LoadingData)
	end
	if _Thread_LoadingEventData ~= nil then
		coroutine.stop(_Thread_LoadingEventData)
	end
	_CallBack_LoadEnd = nil
	_ErrorMsg = nil
end

---取得所有的串檔總數 (一般串檔 & 事件串檔)
local function GetAllDataCount()
	return DataModel.GetDataCount() + EventDataModel.GetEventDataCount()
end

---取得所有已經初始化完成的串檔總數 (一般串檔 & 事件串檔)
local function GetAllDataInitializedCount()
	return _InitializedDataCount + _InitializedEventDataCount
end

---取得 Loading 初始化完成顯示所需
---@param iDataCount number 總數量
---@param iInitializedCount number 已經初始化完成的數量
local function GetInitDataForLoading(iDataCount, iInitializedCount)
	---需要初始化的串檔總數量
	local _TotalCount = iDataCount
	---初始化完的串檔數量
	local _TotalInitializedCount = iInitializedCount
	---要顯示的字串
	local _StringShow = GameTools.ResTextGetter.GetResText("DataInitializing")
	---換算百分比
	local _Percentage = _TotalInitializedCount / _TotalCount
	---串到字串內
	_StringShow = string.format(_StringShow, _Percentage * 100)
	return _StringShow, _Percentage
end

function DataMgr.Init(iCallBack)
	_ProgressFunc = function ()
		if not Loading_Controller:IsInitialized() then
			return
		else
			local _StringShow, _Percentage = GetInitDataForLoading(GetAllDataCount(), GetAllDataInitializedCount())
			Loading_Controller.SetSlider(_Percentage)
			Loading_Controller.SetDetail(_StringShow)
		end
	end

	_Thread_LoadingData = coroutine.start(DataMgr.ReadData)
	_Thread_LoadingEventData = coroutine.start(DataMgr.ReadEventData)

	_CallBack_LoadEnd = iCallBack
end

---讀取各類一般串檔
function DataMgr.ReadData()
	for key, value in pairs(DataModel.GetAllData()) do
		value.Init()
		coroutine.step()  -- 每讀完一個串檔就掛起一次，避免卡住
	end
end

---讀取事件串檔
function DataMgr.ReadEventData()
	for key, value in pairs(EventDataModel.GetAllEventData()) do
		value.Init()
		coroutine.step()
	end
end

---New a Data with coroutine
---@param iNewDataScript XXXData 要讀的串檔
---@param iBinaryReader BinaryReader 串檔讀近來的資料串
---@param iDictionary table 把單筆資料存到各自的 Dictionary ： iDictionary[_Index] = _tempData
---@param iFunctionAddSingleDataByIndex fun(iCountIndex:number, iDataIndex:number, iData:table) 把單筆資料存到各自的特殊處理 iCountIndex：數量的 Index ， iDataIndex：資料內的 Index ， iData：此筆資料
---@param iFunctionAfterDictionaryInitialized fun() 整個串檔讀完後要做的事
function DataMgr.NewData(iNewDataScript, iBinaryReader, iDictionary, iFunctionAddSingleDataByIndex, iFunctionAfterDictionaryAllDataInitialized)
	local _CurrentCoroutine = nil
	_CurrentCoroutine = coroutine.start(function()
		---Lua 檔案名稱
		local _ScriptName = table.GetKey(_G, iNewDataScript)
		--region 判斷有沒有要等其他串檔 有的話 掛起
		local _IsNeedWaitOtherData = iNewDataScript.m_NeedWaitData ~= nil and table.Count(iNewDataScript.m_NeedWaitData) > 0
		while _IsNeedWaitOtherData do
			local _Count = 0
			for key, value in pairs(iNewDataScript.m_NeedWaitData) do
				if value == nil then
					DataMgr.DataMgrLog(3, "File: [".._ScriptName.."] OnLoad Error !!  設定錯誤，請確認參數 m_NeedWaitData ，或確認串檔:["..value.."] 在 Model 內 require 的順序")
					coroutine.stop(_CurrentCoroutine)
				end
				if value.m_IsDataInitialized then
					_Count = _Count + 1
				end
			end
			_IsNeedWaitOtherData = _Count < table.Count(iNewDataScript.m_NeedWaitData)
			if _IsNeedWaitOtherData then
				coroutine.step()
			end
		end
		--endregion
		for i = 1, iNewDataScript.m_DataCount do
			--開始數要讀第幾筆串檔資料
			iNewDataScript.m_NewDataCount = iNewDataScript.m_NewDataCount + 1
			--開始讀檔案囉
			local _Index, _tempData = iNewDataScript:New(iBinaryReader)
			--檢查 檔案 _Index 與 _tempData 不可能為 nil
			if _Index ~= nil and _tempData ~= nil then
				--寫入單筆資料的 ExTable ( C# 內的外掛串表)
				if iNewDataScript.OnLoadExTableData ~= nil then
					_tempData = iNewDataScript.OnLoadExTableData(_Index, _tempData)
				end

				--region 單筆資料存入各串檔的 Dictionary

				--Defult 版
				if iDictionary ~= nil then
					if _Index ~= 0 then
						iDictionary[_Index] = _tempData
					end
				end

				--特殊處裡版
				if iFunctionAddSingleDataByIndex ~= nil then
					if _Index ~= 0 then
						iFunctionAddSingleDataByIndex(i, _Index, _tempData)
					end
				end

				--找不到存入單筆資料的地方 報錯
				if iDictionary == nil and iFunctionAddSingleDataByIndex == nil then
					DataMgr.DataMgrLog(3, "File: [<color=red>".._ScriptName.. "</color>] OnLoad Error !!  找不到資料暫存的地方 請設定 參數: iDictionary 或 iFunctionAddSingleDataByIndex ")
					coroutine.stop(_CurrentCoroutine)
				end
				--endregion

				--region 未讀完 但讀到設定的位元組 掛起

				--目前讀到哪個位元組
				local _StreamReadPosition = tonumber(tostring(iBinaryReader.data.BaseStream.Position))
				--讀了幾次
				local _Times = math.floor(_StreamReadPosition / MAX_DATA_LOAD_ONCE_BYTES)
				--讀檔到達一次讀的位元組數 掛起
				if _Times  > iNewDataScript.m_StreamReadByteTimes and _StreamReadPosition % MAX_DATA_LOAD_ONCE_BYTES >= 0 then
					iNewDataScript.m_StreamReadByteTimes = iNewDataScript.m_StreamReadByteTimes + 1
					coroutine.step()
				end
				--endregion

				--region 讀完 結束讀檔

				--讀到最後一筆資料 結束
				if iNewDataScript.m_NewDataCount == iNewDataScript.m_DataCount then
					--檢查是否有在串檔 table 內
					if not table.Contains(DataModel.m_DataTable, iNewDataScript) and not EventDataModel.Contains(iNewDataScript) then
						DataMgr.DataMgrLog(3, "Need to Add [<color=red>".._ScriptName.. "</color>] to [<color=red>DataModel</color>.<color=green>m_DataTable</color>]")
						coroutine.stop(_CurrentCoroutine)
					end
					iNewDataScript.m_IsDataInitialized = true
					--整個串檔讀完後要做的事
					if iFunctionAfterDictionaryAllDataInitialized ~= nil then
						iFunctionAfterDictionaryAllDataInitialized()
					end
					--一般串檔計數
					if DataModel.Contains(iNewDataScript) then
						_InitializedDataCount = _InitializedDataCount + 1
						DataMgr.DataMgrLog(1, "<color=yellow>Data</color>: [<color=yellow>".._ScriptName.. "</color>] OnLoad End !! DataRowCount = "..iNewDataScript.m_DataCount.. " [<color=yellow>Now InitializedDataCount</color>]： ".._InitializedDataCount)
					end
					--事件串檔計數
					if EventDataModel.Contains(iNewDataScript) then
						_InitializedEventDataCount = _InitializedEventDataCount + 1
						DataMgr.DataMgrLog(1, "<color=orange>EventData</color>: [<color=orange>".._ScriptName.. "</color>] OnLoad End !!  DataRowCount = "..iNewDataScript.m_DataCount.. " [<color=orange>Now InitializedEventDataCount</color>]： ".._InitializedEventDataCount)
					end

					if _ProgressFunc ~= nil then
						_ProgressFunc()
					end
					coroutine.stop(_CurrentCoroutine)
				end
				--endregion
			else
				DataMgr.DataMgrLog(3, "<color=gold> 串檔 </color>: [<color=gold>".._ScriptName.. "</color>] 取得的單筆資料為 nil 或是 串檔結構歪掉，請檢查串檔版本是否與程式碼內的格式相符")
				coroutine.stop(_CurrentCoroutine)
				return
			end
		end
	end)

	--Coroutine 存入各 table 以免要強制關閉的時候找不到人
	if DataModel.Contains(iNewDataScript) then
		DataModel.m_DataCoroutineTable[iNewDataScript.m_ASSET_NAME] = _CurrentCoroutine
	end
	--Coroutine 存入各 table 以免要強制關閉的時候找不到人
	if EventDataModel.Contains(iNewDataScript) then
		EventDataModel.m_EventDataCoroutineTable[iNewDataScript.m_ASSET_NAME] = _CurrentCoroutine
	end
end

function DataMgr.Update()
	if DataMgr.IsAllDataInitialized and _CallBack_LoadEnd ~= nil then
		DataMgr.DataMgrLog(1, "[<color=green>All Data Load Completed</color>] DataCount: ".._InitializedDataCount..", EventDataCount: ".._InitializedEventDataCount)
		_CallBack_LoadEnd(_ErrorMsg)
		_CallBack_LoadEnd = nil
	end
end

---是否 DataMgr 所有的初始化已完成
function DataMgr.IsAllDataInitialized()
	---需要初始化的串檔總數量(目前先以改好 Coroutine 的串檔檢查)
	local _TotalCount = GetAllDataCount()
	---初始化完的串檔數量
	local _TotalInitializedCount = GetAllDataInitializedCount()
	---是否所有串檔初始化完成
	local _IsAllDataInitialized = _TotalInitializedCount == _TotalCount

	if _IsAllDataInitialized then
		_ProgressFunc = nil
	end

	return _IsAllDataInitialized
end

---結束所有 DataMgr 相關的 Coroutine
---@param iIsNeedResetData boolean 是否需要重置 DataMgr 的讀取資料
function DataMgr.StopReadAllData(iIsNeedResetData)
	--停止個各一般串檔讀檔動作
	StopReadData(DataModel.GetAllData(), iIsNeedResetData)
	--停止各事件串檔讀檔動作
	StopReadData(EventDataModel.GetAllEventData(), iIsNeedResetData)
	--停止全部讀檔
	SetMgrAllCoroutineDead()
end

---初始化完成的事件串檔數量 +1
function DataMgr.AddInitializedEventDataCount()
	_InitializedEventDataCount = _InitializedEventDataCount + 1
end

---取得初始化完成的事件串檔數量
function DataMgr.GetInitializedEventDataCount()
	return _InitializedEventDataCount
end

---串檔 Log 
---金手指有開啟才會顯示
---@param iLogType int 要顯示訊息的類型 ( 1一般 2警告 3錯誤 )
---@param iString string 要顯示的訊息
function DataMgr.DataMgrLog(iLogType,iString)
	if ProjectMgr.IsDebug() then
		--DataMgrDebug 沒開 就不顯示
		if this.m_IsOpenDataMgrDebugLog then
			iString = "[<color=#0080FF>DataMgr</color>] "..iString
			if iLogType == 1 then
				D.Log(iString)
			elseif iLogType == 2 then
				D.LogWarning(iString)
			elseif iLogType == 3 then
				D.LogError(iString)
			end
		end
	end
end


---取得所有的串檔總數 (一般串檔 & 事件串檔)
local function GetLanguageDataCount()
	return table.Count(DataModel.m_ChangeLanguageRnewTable) + table.Count(EventDataModel.m_ChangeLanguageRenewTable) 
end

---取得所有已經初始化完成的串檔總數 (一般串檔 & 事件串檔)
local function GetLanguageDataInitializedCount()
	return _InitializedDataCount + _InitializedEventDataCount
end

---設定換語言切換專屬的檔案讀取檢查
function DataMgr.SetProcessFunc()
		_InitializedDataCount = 0 
		_InitializedEventDataCount = 0
	_ProgressFunc = function()
		
		if UIMgr.GetUIStage(Loading_Controller) < EUIStage.Initialized then
			return
		else
			local _StringShow, _Percentage = GetInitDataForLoading(GetLanguageDataCount(), GetLanguageDataInitializedCount())
			Loading_Controller.SetSlider(_Percentage)
			Loading_Controller.SetDetail(_StringShow)
		end
	end
end

---檢查因為語言切換造成串表重新初始化是否完成
function DataMgr.CheckIsLanguageRelativeDataInitFinish()

	---一般串檔table資料開始初始化
	for key, value in pairs(DataModel.m_ChangeLanguageRnewTable) do
		if value.m_IsDataInitialized==false then
			return false
		end
	end
	---事件串檔table資料開始初始化
	for key, value in pairs(EventDataModel.m_ChangeLanguageRenewTable) do
		if value.m_IsDataInitialized==false then
			return false
		end
	end

	return true
end

---根據語言重新指派各串檔要讀取的檔案
function DataMgr.ReassignedFileAccordingToLanguage()

	local _LanguageIndex = ClientSaveMgr.GetDataTable(EClientSaveDataType.Device).m_LastLanguage
	local _NameTail = GString.GetLanguageTailText()

	---一般串檔 切換參考檔案
	for key, value in pairs(DataModel.m_ChangeLanguageRnewTable) do

		local _NameTable ={} 
		for match in (value.m_ASSET_NAME .. "_"):gmatch("(.-)_") do
			table.insert(_NameTable, match)
		end
		value.m_ASSET_NAME = _NameTable[1] .. "_" .. _NameTail
	end

	---事件串檔 切換參考檔案
	for key, value in pairs(EventDataModel.m_ChangeLanguageRenewTable) do
		local _NameTable ={} 
		for match in (value.m_ASSET_NAME .. "_"):gmatch("(.-)_") do
			table.insert(_NameTable, match)
		end
		value.m_ASSET_NAME = _NameTable[1] .. "_" .. _NameTail
	end
end

function DataMgr.AddInitializedDataCount()
	_InitializedDataCount = _InitializedDataCount + 1
end

return DataMgr
