---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---功能選單介面
---@class Menu_Controller
---author 鼎翰
---telephone #2917
---version 1.0
---since [黃易群俠傳M] 0.91
---date 2024.1.4
Menu_Controller = {}
local this = Menu_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("Menu_View", "Menu_Controller", EUIOrderLayers.HalfPage_Right)

---回到主介面時 需要打開除了常駐群組外的群組
this.m_NeedOpenOthersWhenBackMainView = false

---登入後第一次開啟 Menu
this.m_IsLoginFirstOpenMenu = true

---主介面按鈕功能區劃分
---@class EMenuGroup
EMenuGroup = {
	---常駐
	Permanent = 1,
	---時光機
	TimeMachine = 2,
	---遊戲功能區_遊玩導向
	GameArea_Play = 3,
	---遊戲功能區_功能導向
	GameArea_Functional = 4,
	---系統功能區_營運導向
	SystemArea_Operations = 5,
	---系統功能區_系統導向
	SystemArea_Systems = 6,
}

---主選單的按鈕狀態
---@class EMenuButtonState
EMenuButtonState = {
	Normal = 1,
	Hide = 2,
}

---主選單更新方式
---@class EMenuUpdateType
EMenuUpdateType = {
	--永標
	StaticFlag = 1,
	---等級
	Level = 2,

	--後面就是特殊檢查 自己加類別 m_SpecialOpeningInspection--

	---特殊 時光機艙室啟動
	TimeMachineRoomActivated = 3,
}

---主選單群組按鈕資料_依照永標
this.m_MenuButtonData_StaticFlag = {}

---主選單群組按鈕資料_依照等級
this.m_MenuButtonData_Level = {}

---主選單群組按鈕資料_依照特殊檢查
this.m_MenuButtonData_Special = {}

---主選單各群組的位置
this.m_Transform_MenuButtonGroup = {}

---@type table <number, MenuButtonData> 常駐群組用
local m_GroupAlways_Objects = {}

---@type table <number, UIController> Menu 有的 UI
local m_UIControllerInMenu = {}

---@type table <number, m_MainButtonCircleRotatetData>主要控制開關區域按鈕 圖片的Table
local m_MainButtonCircleRotate = {}

---@type table <EMenuGroup, table<number, MenuButtonData> > 系統群組排序用
local m_GroupSystem = {}
m_GroupSystem[EMenuGroup.SystemArea_Operations] = {}
m_GroupSystem[EMenuGroup.SystemArea_Systems] = {}

---@type table <EMenuGroup, table<number, MenuButtonData> > 要播動畫的群組GameObject放這邊
local m_GroupMain_Objects = {}
m_GroupMain_Objects[EMenuGroup.TimeMachine] = {}
m_GroupMain_Objects[EMenuGroup.GameArea_Play] = {}
m_GroupMain_Objects[EMenuGroup.GameArea_Functional] = {}

---@type table<EMenuGroup, UIVisibleBatch> 批次顯示資料
local m_VisableBatch = {}

---開啟GroupMenu的按鍵 使用圖片
---GroupMenu 關閉中 使用圖片
local m_Button_Main_Sprite_Closed =  "MainBtn_92" 
---GroupMenu 開啟中 使用圖片
local m_Button_Main_Sprite_Open =  "MainBtn_93"

---最多顯示幾個主選的按鈕
local m_MaxGroupPermanentBtnAmount = 5

--- 顯示遊樂場按鍵
this.m_ShowPlayGroundBtn = false

---檢查按鈕狀態
local function CheckMainButtonState()
	if this.m_GroupBtnActive then
		if not this.m_GObj_BtnOpenMenu:IsSelect() then
			this.m_GObj_BtnOpenMenu.m_ButtonEx:DoButtonStateTransition(ESelectionState.Selected, true)
		end
	else
		if this.m_GObj_BtnOpenMenu:IsSelect() then
			this.m_GObj_BtnOpenMenu.m_ButtonEx:DoButtonStateTransition(ESelectionState.Normal, true)
		end
	end

	local _UseSpriteName = this.m_GroupBtnActive and m_Button_Main_Sprite_Open or m_Button_Main_Sprite_Closed
	SpriteMgr.Load(_UseSpriteName,this.m_Image_BtnOpenMenu)
end

---檢查分隔線
---@param iGroupTransform Transform 要檢查的群組的位置
local function CheckDividerActive(iGroupTransform)
	local _ChildCount = iGroupTransform.childCount
	local _Count = 0

	--開始算有幾個正在顯示
	for i = 0, _ChildCount - 1 do
		local _Child = iGroupTransform:GetChild(i)
		if _Child.gameObject.activeSelf then
			_Count = _Count + 1
		end
	end

	local _Gobj_Divider = iGroupTransform == this.m_Transform_MenuButtonGroup[EMenuGroup.GameArea_Play] and this.m_GObj_Divider_GameArea_Play or this.m_GObj_Divider_GameArea_Functional

	if _Count > 0 then
		_Gobj_Divider:SetActive(true)
	else
		_Gobj_Divider:SetActive(false)
	end
end

---設定主選單狀態
local function SetMenuState(iMenuActiveKind)
	--如果是開啟狀態
	if this.m_GroupBtnActive == true then
		--主選單顯示類別為 0 就加入主介面開關
		if iMenuActiveKind == EMenuActiveKind.KeepOpen then
			this.m_NeedOpenOthersWhenBackMainView = true
		end
	end
end

---功能選單按鈕 Onclick
local function OnClickMenuButton(iMenuButtonData)
	---開啟類型
	local _MenuActiveKind = iMenuButtonData.m_Data.m_MenuActiveKind
	---UI Index
	local _UIIndex = iMenuButtonData.m_Data.m_OpenUIIndex
	---按鈕目前狀態
	local _CurrentButtonState = iMenuButtonData.m_ButtonData.m_ButtonState
	--判斷按鈕狀態 一般狀態則觸發開關介面
	if _CurrentButtonState == EMenuButtonState.Normal then
		--依照主選單顯示類別 開關主選單
		if _MenuActiveKind <= 1 then
			---目前 UI 是開還是關
			SetMenuState(_MenuActiveKind)
			---分頁 Index
			local _OpenTabIndex = iMenuButtonData.m_Data.m_OpenTabIndex == 0 and nil or iMenuButtonData.m_Data.m_OpenTabIndex
			---後續參數
			local _OpenParam = iMenuButtonData.m_Data.m_OpenParam == 0 and nil or iMenuButtonData.m_Data.m_OpenParam
			UIMgr.CheckAndSetUIVisible(_UIIndex, _OpenTabIndex, _OpenParam)
		elseif _MenuActiveKind == 2 then
			--觸發事件ID
			if iMenuButtonData.m_EvtKind > 0 then
				SendProtocol_006._023(iMenuButtonData.m_EvtKind)
			end
		end
	--隱藏狀態則 顯示中央訊息
	else
		SetMenuState(EMenuActiveKind.KeepOpen)
		if not string.IsNullOrEmpty(iMenuButtonData.m_Data.m_NotOpenPromptText) then
			MessageMgr.AddCenterMsg(false, iMenuButtonData.m_Data.m_NotOpenPromptText)
		end
	end
	CheckMainButtonState()
end

---取得功能選單按鈕的副物件
local function GetMenuButtonParent(iGroupID)
	return this.m_Transform_MenuButtonGroup[iGroupID]
end

---取得功能選單按鈕的實體
local function GetMenuButtonInstantiate(iGroupID)
	---父物件
	local _Trans_Parents = GetMenuButtonParent(iGroupID)
	---實例
	local _Instance = nil

	if iGroupID == EMenuGroup.Permanent then
		_Instance = this.m_GObj_MenuButtonRef_Always:Instantiate(_Trans_Parents)
	elseif iGroupID == EMenuGroup.SystemArea_Operations or iGroupID == EMenuGroup.SystemArea_Systems then
		_Instance = this.m_GObj_MenuButtonRef_System:Instantiate(_Trans_Parents)
	else
		_Instance = this.m_GObj_MenuButtonRef_Other:Instantiate(_Trans_Parents)
	end

	return _Instance
end


local function SetAllActive(iActive)
	if this.m_GObj_GroupMain then
		---按鈕列是否開放中
		this.m_GroupBtnActive = iActive

		--主按鈕顯示
		--this.m_GObj_BtnOpenMenu:SetActive(not this.m_GroupBtnActive)

		--常駐群組按鈕字串 顯示設定
		for key, value in pairs(m_GroupAlways_Objects) do
			value.m_ButtonData.m_GObj_ButtonText:SetActive(this.m_GroupBtnActive)
		end

		for i = 1, table.Count(m_MainButtonCircleRotate) do
			m_MainButtonCircleRotate[i].m_LeanTweenVisual.enabled = this.m_GroupBtnActive
			m_MainButtonCircleRotate[i].m_Transform.localRotation = Vector3.zero
		end

		--主要介面作動
		this.m_GObj_GroupMain:SetActive(this.m_GroupBtnActive)

		--關閉按鈕顯示
		this.m_GObj_ButtonCloseMenu:SetActive(this.m_GroupBtnActive)
		if this.m_GroupBtnActive then
			UIMgr.UpdateUIOrderByUIController(this)
		end

		if iActive then
			---開啟時的特殊動畫
			for k,v in pairs(m_VisableBatch) do
				m_VisableBatch[k]:Start(0)
			end
		else
			for k,v in pairs(m_VisableBatch) do
				m_VisableBatch[k]:Stop()
			end
		end
		
		UIMgr.SwitchViewVisible(this, not iActive)
	end
end

---更新按鈕狀態
---@param iMenuButtonData MenuButtonData 目前要更新的按鈕
local function UpdateMenuButtonState(iMenuButtonData, iIsActive)
	if iIsActive then
		--之前沒顯示的顯示
		if not iMenuButtonData.m_ButtonData.m_Button.gameObject.activeSelf then
			iMenuButtonData.m_ButtonData.m_Button.gameObject:SetActive(true)
		end

		if iMenuButtonData.m_ButtonData.m_ButtonState == EMenuButtonState.Hide then
			iMenuButtonData.m_ButtonData.m_Button.m_ButtonEx:DoButtonStateTransition(ESelectionState.Normal)
		end
		--改按鈕狀態 正常
		iMenuButtonData.m_ButtonData.m_ButtonState = EMenuButtonState.Normal
	--不顯示的話
	else
		--需要隱藏的話
		if iMenuButtonData.m_IsNeedHideWhenNotOpen then
			--金手指要顯示
			if ProjectMgr.IsDebug() and not UIMgr.m_IsUseUIStaticFlagCtrl then
				--之前沒顯示的顯示
				iMenuButtonData.m_ButtonData.m_Button.gameObject:SetActive(true)
			else
				iMenuButtonData.m_ButtonData.m_Button.gameObject:SetActive(false)
			end
		--要顯示的話
		else
			-- 設定按鈕效果狀態
			iMenuButtonData.m_ButtonData.m_Button.m_ButtonEx:DoButtonStateTransition(ESelectionState.Disabled)
			--之前沒顯示的顯示
			iMenuButtonData.m_ButtonData.m_Button.gameObject:SetActive(true)
		end
		--改按鈕狀態 隱藏
		iMenuButtonData.m_ButtonData.m_ButtonState = EMenuButtonState.Hide
	end
end

local function CheckButtonActiveState(iMenuButtonData)
	if iMenuButtonData.m_Data then
		local _IsActive = true
		local _StaticFlagID = iMenuButtonData.m_Data.m_Flag
		local _IsHaveStaticFlag = _StaticFlagID == 0 and true or PlayerData.IsHaveStaticFlag(_StaticFlagID)
		local _IsLv = _IsHaveStaticFlag and PlayerData.GetLv() >= iMenuButtonData.m_Data.m_Lv
		local _SpecialOpeningInspection = iMenuButtonData.m_Data.m_SpecialOpeningInspection
		local _IsSpecialOpening = _SpecialOpeningInspection == 0 or 
		(_SpecialOpeningInspection >= 1 and TimeMachineMgr.GetRoomState(_SpecialOpeningInspection) > 0)

		_IsActive = _IsHaveStaticFlag and _IsLv and _IsSpecialOpening

		if iMenuButtonData.m_ButtonData.m_Button.gameObject.name == "UI111" then
			D.Log("UI111 Button Active State: " .. tostring(iMenuButtonData.m_ButtonData.m_ButtonState))
		end

		UpdateMenuButtonState(iMenuButtonData, _IsActive)
		
	end
end

---找舊圖
---@param iMenuButtonData MenuButtonData 按鈕資料
---@param iUIIconName string Icon 的圖片
local function SetWithOldName(iMenuButtonData, iUIIconName)
	---舊圖
	SpriteMgr.Load(iUIIconName, function (iSprite)
		local _UIImageChange = iMenuButtonData.m_UIImageChange
		if _UIImageChange ~= nil then
			_UIImageChange.m_GroupRenderInfo:SetRenderValue(ESelectionState.Normal, iSprite)
			_UIImageChange.m_GroupRenderInfo:SetRenderValue(ESelectionState.Highlighted, iSprite)
			_UIImageChange.m_GroupRenderInfo:SetRenderValue(ESelectionState.Pressed, iSprite)
			_UIImageChange.m_GroupRenderInfo:SetRenderValue(ESelectionState.Selected, iSprite)
			_UIImageChange.m_GroupRenderInfo:SetRenderValue(ESelectionState.Disabled, iSprite)
			_UIImageChange:Trigger(ESelectionState.Normal)
			CheckButtonActiveState(iMenuButtonData)
		end
	end)
end

---設指定狀態圖片為 Normal
---@param iMenuButtonData MenuButtonData 按鈕資料
---@param iESelectionState ESelectionState 要設定圖片的狀態
---@param iUIIconName string Icon 的圖片名稱
local function SetToNormalSprite(iMenuButtonData, iESelectionState, iUIIconName)
	SpriteMgr.Load(iUIIconName.."_D", function (iSprite)
		if iSprite ~= nil then
			local _UIImageChange = iMenuButtonData.m_UIImageChange
			if _UIImageChange ~= nil then
				_UIImageChange.m_GroupRenderInfo:SetRenderValue(iESelectionState, iSprite)
				_UIImageChange:Trigger(ESelectionState.Normal)
			end
			if _UIImageChange.m_GroupRenderInfo:GetRenderValue(ESelectionState.Disabled) ~= nil and _UIImageChange.m_GroupRenderInfo:GetRenderValue(ESelectionState.Normal) then
				CheckButtonActiveState(iMenuButtonData)
			end
		end
	end)
end

---設定圖片
---@param iMenuButtonData MenuButtonData 按鈕資料
---@param iESelectionState ESelectionState 要設定圖片的狀態
---@param iUIIconName string Icon 的圖片名稱
local function SetSprite(iMenuButtonData, iESelectionState, iUIIconName)
	local _CurrnetName = ""
	if iESelectionState == ESelectionState.Normal then
		_CurrnetName = iUIIconName.."_D"
	elseif iESelectionState == ESelectionState.Highlighted then
		_CurrnetName = iUIIconName.."_H"
	elseif iESelectionState == ESelectionState.Pressed then
		_CurrnetName = iUIIconName.."_S"
	elseif iESelectionState == ESelectionState.Selected then
		_CurrnetName = iUIIconName.."_A"
	elseif iESelectionState == ESelectionState.Disabled then
		_CurrnetName = iUIIconName.."_L"
	end
	local _UIImageChange = iMenuButtonData.m_UIImageChange
	SpriteMgr.Load(_CurrnetName, function (iSprite)
		if iSprite ~= nil then
			if _UIImageChange ~= nil then
				_UIImageChange.m_GroupRenderInfo:SetRenderValue(iESelectionState, iSprite)
				_UIImageChange:Trigger(ESelectionState.Normal)
			end
			if _UIImageChange.m_GroupRenderInfo:GetRenderValue(ESelectionState.Disabled) ~= nil and _UIImageChange.m_GroupRenderInfo:GetRenderValue(ESelectionState.Normal) then
				CheckButtonActiveState(iMenuButtonData)
			end
		else
			SetToNormalSprite(iMenuButtonData, iESelectionState, iUIIconName)
		end
	end)
end

---設定 Icon 圖片
---@param iMenuButtonData MenuButtonData 按鈕資料
---@param iUIIconName string Icon 的圖片
local function SetIconWithIconName(iMenuButtonData, iUIIconName)
	---判斷是不是舊圖
	local _IsOld = string.find(iUIIconName, "Main") == nil
	---是的話 改小i
	if _IsOld then
		SetWithOldName(iMenuButtonData, iUIIconName:gsub('I','i'))
	else
		SetSprite(iMenuButtonData, ESelectionState.Normal, iUIIconName)
		SetSprite(iMenuButtonData, ESelectionState.Highlighted, iUIIconName)
		SetSprite(iMenuButtonData, ESelectionState.Pressed, iUIIconName)
		SetSprite(iMenuButtonData, ESelectionState.Selected, iUIIconName)
		SetSprite(iMenuButtonData, ESelectionState.Disabled, iUIIconName)
	end
end

---產生遊樂場按鈕
local function CreatePlaygroundButton()
	local _MenuButtonData = {}
	_MenuButtonData.m_Data = nil
	---實際按鈕資料
	_MenuButtonData.m_ButtonData = {}
	---按鈕的狀態
	_MenuButtonData.m_ButtonData.m_ButtonState = EMenuButtonState.Normal
	---按鈕本人
	_MenuButtonData.m_ButtonData.m_Button = Button.New(GetMenuButtonInstantiate(EMenuGroup.Permanent))
	---取得 UIImageChange
	_MenuButtonData.m_UIImageChange = _MenuButtonData.m_ButtonData.m_Button.transform:Find("Image_Icon"):GetComponentInChildren(typeof(UIImageChange))
	--取得 UIImageChange Image
	_MenuButtonData.m_Image_ImageChange = _MenuButtonData.m_UIImageChange:GetComponent(typeof(Image))
	--Icon 換圖設定
	SetIconWithIconName(_MenuButtonData, "MainIcon_004")
	---按鈕字串
	_MenuButtonData.m_ButtonData.m_GObj_ButtonText = _MenuButtonData.m_ButtonData.m_Button.transform:Find("BtnText"):GetComponent("Transform").gameObject
	---改名稱
	_MenuButtonData.m_ButtonData.m_Button.gameObject.name = "遊樂場"
	---設定按鈕名稱
	Button.SetText(_MenuButtonData.m_ButtonData.m_Button, "遊樂場")
	---加按鈕 Event
	Button.AddListener(_MenuButtonData.m_ButtonData.m_Button, EventTriggerType.PointerClick, function()
		UIMgr.Open(Playground_Controller)
	end)

	_MenuButtonData.m_ButtonData.m_Button.gameObject:SetActive(true)

	this.m_Obj_PlayGroundBtn = _MenuButtonData.m_ButtonData.m_Button.gameObject

	table.insert(m_GroupAlways_Objects, _MenuButtonData)
end


local function InitGroupButtons(iGroupID)
	---目前要初始話的群組資料
	local _GroupData = MenuData.GetByGroup(iGroupID)
	---排序用的 Table
	local _SortTable = {}
	---新增排序後的Table
	local _sortedButtons = {} 
	--有取到資料才做事
	if _GroupData ~= nil and type(_GroupData) == "table" then

		---依照群組 取各資料 Table
		local _DataTable = {}
		if iGroupID == EMenuGroup.Permanent then
			_DataTable = m_GroupAlways_Objects
		elseif iGroupID == EMenuGroup.SystemArea_Operations or iGroupID == EMenuGroup.SystemArea_Systems then
			_DataTable = m_GroupSystem[iGroupID]
		else
			_DataTable = m_GroupMain_Objects[iGroupID]
		end

		---已經生成幾個群組編號為1的按鍵
		local _PermanentBtnAmount = 0
			
		for key, _MenuData in pairs(_GroupData) do
			---@type MenuButtonData 每個按鈕的資料結構
			local _MenuButtonData = {}
			_MenuButtonData.m_Data = _MenuData
			---實際按鈕資料
			_MenuButtonData.m_ButtonData = {}
			---按鈕的狀態
			_MenuButtonData.m_ButtonData.m_ButtonState = EMenuButtonState.Normal
			

			local _BuildBtn = function()
				---按鈕本人
				_MenuButtonData.m_ButtonData.m_Button = Button.New(GetMenuButtonInstantiate(iGroupID))
				---常駐 跟其他的按鈕不一樣 少一層
				if iGroupID == EMenuGroup.Permanent then
					_MenuButtonData.m_ButtonData.m_GObj_ButtonText = _MenuButtonData.m_ButtonData.m_Button.transform:Find("BtnText"):GetComponent("Transform").gameObject
					_MenuButtonData.m_ButtonData.m_GObj_ButtonRedDot = _MenuButtonData.m_ButtonData.m_Button.transform:Find("Image_RedPoint"):GetComponent("Transform").gameObject
				else
					_MenuButtonData.m_ButtonData.m_GObj_ButtonText = _MenuButtonData.m_ButtonData.m_Button.transform:GetChild(0):Find("BtnText"):GetComponent("Transform").gameObject
					_MenuButtonData.m_ButtonData.m_GObj_ButtonRedDot = _MenuButtonData.m_ButtonData.m_Button.transform:GetChild(0):Find("Image_RedPoint"):GetComponent("Transform").gameObject
				end

				local _RedDotTable  = RedTableClass.NewData(_MenuButtonData.m_ButtonData.m_GObj_ButtonRedDot, _MenuData.m_OpenUIIndex, _MenuData.m_OpenTabIndex)
				local _ERedDotUIPos = iGroupID == EMenuGroup.Permanent and ERedDotUIPos.Menu_AlwaysShow or ERedDotUIPos.Menu_DownPage
				RedDotMgr.AssignUICorrelation(_ERedDotUIPos,_RedDotTable)

				---找看看有沒有動畫
				local _Anim = _MenuButtonData.m_ButtonData.m_Button.transform:Find("Panel_Animation")
				---有動畫的話要改接下來找的物件層級
				local findTransform = _MenuButtonData.m_ButtonData.m_Button.transform
				--設定動畫
				if _Anim then
				findTransform = _Anim
				_MenuButtonData.gameObject = _Anim.gameObject
				end

				---按鈕名稱
				local _ButtonName = string.format("UI%03d", _MenuButtonData.m_Data.m_OpenUIIndex)
				--按鈕名稱 如果有子分頁就加子分頁
				if _MenuButtonData.m_Data.m_OpenTabIndex > 0 then
				_ButtonName = _ButtonName..string.format("_%02d", _MenuButtonData.m_Data.m_OpenTabIndex)
				end

				---改名稱
				_MenuButtonData.m_ButtonData.m_Button.gameObject.name = _ButtonName
				---取得 UIImageChange
				_MenuButtonData.m_UIImageChange = findTransform:Find("Image_Icon"):GetComponentInChildren(typeof(UIImageChange))
				--取得 UIImageChange Image
				_MenuButtonData.m_Image_ImageChange = _MenuButtonData.m_UIImageChange:GetComponent(typeof(Image))
				---取得 UIRenderTMPTextChangeStyle
				_MenuButtonData.m_UIRenderTMPTextChangeStyle = findTransform:Find("BtnText"):GetComponentInChildren(typeof(UIRenderTMPTextChangeStyle))
				--Icon 換圖設定
				SetIconWithIconName(_MenuButtonData, _MenuButtonData.m_Data.m_ButtonIconName)
				---設定按鈕名稱
				Button.SetText(_MenuButtonData.m_ButtonData.m_Button, _MenuButtonData.m_Data.m_ButtonText)
				---加按鈕 Event
				Button.AddListener(_MenuButtonData.m_ButtonData.m_Button, EventTriggerType.PointerClick, OnClickMenuButton, _MenuButtonData)

				--加入各資料 Table
				table.insert(_DataTable, _MenuButtonData)

				--加入排序 Table
				table.insert(_SortTable, key)

				---要存到搜索字典內的 結構
				local _CurrentButtonData = {
				---群組ID
				m_GroupID = iGroupID,
				---限制 等級
				m_Lv = _MenuData.m_Lv,
				---開啟UI Index
				m_UIIndex = _MenuData.m_OpenUIIndex,
				---限制 永標
				m_StaticFlag = _MenuData.m_Flag,
				---限制 特殊
				m_SpecialOpeningInspection = _MenuData.m_SpecialOpeningInspection,
				---未開放時 是否要隱藏
				m_IsNeedHideWhenNotOpen = _MenuData.m_IsNeedHideWhenNotOpen,
				---產生出來的按鈕資料
				m_ButtonData = _MenuButtonData.m_ButtonData,
				---按紐圖片變化的 Script
				m_UIImageChange = _MenuButtonData.m_UIImageChange,
				---按紐圖片變化的 Image
				m_Image_ImageChange = _MenuButtonData.m_Image_ImageChange,
				---按鈕文字類型變化的 Script
				m_UIRenderTMPTextChangeStyle = _MenuButtonData.m_UIRenderTMPTextChangeStyle
				}

				--存入 永標搜索 Table
				if this.m_MenuButtonData_StaticFlag[_MenuData.m_Flag] == nil then
				this.m_MenuButtonData_StaticFlag[_MenuData.m_Flag] = {}
				end
				table.insert(this.m_MenuButtonData_StaticFlag[_MenuData.m_Flag], _CurrentButtonData)
			
				--存入 等級搜索 Table
				if this.m_MenuButtonData_Level[_MenuData.m_Lv] == nil then
					this.m_MenuButtonData_Level[_MenuData.m_Lv] = {}
				end
				table.insert(this.m_MenuButtonData_Level[_MenuData.m_Lv], _CurrentButtonData)
			
				--存入 特殊檢查搜索 Table
				if this.m_MenuButtonData_Special[iGroupID] == nil then
					this.m_MenuButtonData_Special[iGroupID] = {}
				end
				table.insert(this.m_MenuButtonData_Special[iGroupID], _CurrentButtonData)

				---要開的 UI
				local _UIController = UISetting.GetUIControllerByUIIndex(_MenuData.m_OpenUIIndex)
				--加入 Menu 有的 UI Table
				if _UIController ~= nil and not table.Contains(m_UIControllerInMenu, _UIController) then
				table.insert(m_UIControllerInMenu, _UIController)
				end

			end

			---群組1 常駐只要生成前5個按鍵
			if iGroupID == EMenuGroup.Permanent then
				_PermanentBtnAmount = _PermanentBtnAmount +1

				if _PermanentBtnAmount <=m_MaxGroupPermanentBtnAmount then
					_BuildBtn()
				end
			else
				_BuildBtn()
			end
		end
		
		---遊樂場開關 Debug 時才會出現
		if ProjectMgr.IsDebug() and iGroupID == EMenuGroup.Permanent then
			CreatePlaygroundButton()
			--- m_ShowPlayGroundBtn 默認false 所以會將按鍵關閉顯示 要開要去金手指開啟
			Menu_Controller.ShowPlayGroundBtn()
		end

		--群組初始化完成 開始排序
		table.sort(_SortTable)

		--開始排序
		--常駐區域 和 系統 系統區域 都是倒序
		if iGroupID == EMenuGroup.Permanent or iGroupID == EMenuGroup.SystemArea_Systems then
			for i = 1, #_SortTable do
				for key, value in pairs(_DataTable) do
					if value.m_Data ~= nil and value.m_Data.m_Idx == _SortTable[i] then
						value.m_ButtonData.m_Button.transform:SetSiblingIndex(#_SortTable - i)
					end
				end
			end
		--其他 都是正序
		else
			for i = 1, #_SortTable do
				for key, value in pairs(_DataTable) do
					if value.m_Data.m_Idx == _SortTable[i] then
						value.m_ButtonData.m_Button.transform:SetSiblingIndex(i - 1)
						if iGroupID ~= EMenuGroup.SystemArea_Operations then
							---保存排序後的結果
							table.insert(_sortedButtons, value) 
						end
					end
				end
			end
		end
	end
	
	---此次點擊在Menu 有按鍵的範圍內
	this.m_Trans_ClickInMenuArea = this.m_ViewRef.m_Dic_Trans:Get("&ClickInMenuArea").gameObject:GetComponent("RectTransform")
	---返回排序後的結果
	return _sortedButtons
end

---初始化所有按鈕
local function InitAllButtons()
    for key, value in pairs(EMenuGroup) do
		---獲取排序後的按鈕列表
        local _sortedButtons = InitGroupButtons(value) 
        if _sortedButtons and #_sortedButtons > 0 then
            m_VisableBatch[value] = UIVisibleEffect.AddUIVisibleBatch(_sortedButtons, 0.02)
        end
    end
end

---依照永標更新按鈕狀態
local function UpdateButtonStateByStaticFlag(iStaticFlagID)
	---更新的按鈕 Table
	local _CurrentButtonTable = this.m_MenuButtonData_StaticFlag[iStaticFlagID]
	if _CurrentButtonTable ~= nil then
		---玩家是否有永標
		local _IsHaveStaticFlag = iStaticFlagID == 0 and true or PlayerData.IsHaveStaticFlag(iStaticFlagID)
		for key, value in pairs(_CurrentButtonTable) do
			---按鈕是否要顯示
			local _IsActive = _IsHaveStaticFlag and PlayerData.GetLv() >= value.m_Lv
			UpdateMenuButtonState(value, _IsActive)
		end
	end
end

---是否需要更新主選單按鈕
local function IsNeedUpdateButtonState(iStaticFlagID)
	return table.ContainsKey(this.m_MenuButtonData_StaticFlag, iStaticFlagID)
end

---依照等級更新按鈕狀態
local function UpdateButtonStateByLevel(iLevel)
	---更新的按鈕 Table
	local _CurrentButtonTable = this.m_MenuButtonData_Level[iLevel]
	if _CurrentButtonTable ~= nil then
		for key, value in pairs(_CurrentButtonTable) do
			---按鈕是否要顯示
			local _IsActive = value.m_StaticFlag == 0 and true or PlayerData.IsHaveStaticFlag(value.m_StaticFlag)
			UpdateMenuButtonState(value, _IsActive)
		end
	end
end

---是否需要更新主選單按鈕
local function IsNeedUpdateButtonStateWithLevelUp(iLevel)
	return table.ContainsKey(this.m_MenuButtonData_Level, iLevel)
end

---依照特殊狀況更新按鈕狀態
local function UpdateButtonStateByGroupTimeMachine()
	for key, value in pairs(this.m_MenuButtonData_Special[EMenuGroup.TimeMachine]) do
		local _IsActive = value.m_SpecialOpeningInspection == 0 or 
		(value.m_SpecialOpeningInspection >= 1 and TimeMachineMgr.GetRoomState(value.m_SpecialOpeningInspection) > 0)
		UpdateMenuButtonState(value, _IsActive)
	end
end

---初始化
function Menu_Controller.Init()
	---開啟 Menu 按鈕
	this.m_GObj_BtnOpenMenu = Button.New(this.m_ViewRef.m_Dic_Trans:Get("&Button_Main").gameObject)
	---開啟 Menu 按鈕縮放
	this.m_UIRenderScale_BtnOpenMenu = this.m_ViewRef.m_Dic_Trans:Get("&Button_Main_Image_Icon"):GetComponent("UIRenderScale")
	---開關GroupMenu按鍵 使用的圖片 
	this.m_Image_BtnOpenMenu = this.m_ViewRef.m_Dic_Trans:Get("&Button_Main_Image_Icon"):GetComponent("Image")
	Button.AddListener(this.m_GObj_BtnOpenMenu, EventTriggerType.PointerClick, Menu_Controller.OnClick_OpenMenu)
	Button.AddListener(this.m_GObj_BtnOpenMenu, EventTriggerType.PointerUp, function()
		HEMTimeMgr.DoFunctionDelay(0.1,function()
			this.m_UIRenderScale_BtnOpenMenu:Trigger(ESelectionState.Normal)
		end)
	end)

	m_MainButtonCircleRotate[1] = {}
	m_MainButtonCircleRotate[1].m_LeanTweenVisual = this.m_ViewRef.m_Dic_Trans:Get("&Image_Circle_BG"):GetComponent("LeanTweenVisual")
	m_MainButtonCircleRotate[1].m_Transform = this.m_ViewRef.m_Dic_Trans:Get("&Image_Circle_BG")

	m_MainButtonCircleRotate[2] = {}
	m_MainButtonCircleRotate[2].m_LeanTweenVisual = this.m_ViewRef.m_Dic_Trans:Get("&Image_Circle_01"):GetComponent("LeanTweenVisual")
	m_MainButtonCircleRotate[2].m_Transform = this.m_ViewRef.m_Dic_Trans:Get("&Image_Circle_01")

	m_MainButtonCircleRotate[3] = {}
	m_MainButtonCircleRotate[3].m_LeanTweenVisual = this.m_ViewRef.m_Dic_Trans:Get("&Image_Circle_03"):GetComponent("LeanTweenVisual")
	m_MainButtonCircleRotate[3].m_Transform = this.m_ViewRef.m_Dic_Trans:Get("&Image_Circle_03")

	m_MainButtonCircleRotate[4] = {}
	m_MainButtonCircleRotate[4].m_LeanTweenVisual = this.m_ViewRef.m_Dic_Trans:Get("&Image_Circle_04"):GetComponent("LeanTweenVisual")
	m_MainButtonCircleRotate[4].m_Transform = this.m_ViewRef.m_Dic_Trans:Get("&Image_Circle_04")

	m_MainButtonCircleRotate[5] = {}
	m_MainButtonCircleRotate[5].m_LeanTweenVisual = this.m_ViewRef.m_Dic_Trans:Get("&Image_Circle_06"):GetComponent("LeanTweenVisual")
	m_MainButtonCircleRotate[5].m_Transform = this.m_ViewRef.m_Dic_Trans:Get("&Image_Circle_06")


	---關閉 Menu 按鈕
	this.m_GObj_ButtonCloseMenu = this.m_ViewRef.m_Dic_Trans:Get("&ButtonCloseMenu").gameObject
	Button.AddListener(this.m_GObj_ButtonCloseMenu, EventTriggerType.PointerClick, Menu_Controller.Close)

	---其他區域的 ContentFitterImmediate
	this.m_ContentFitterImmediate = this.m_ViewRef.m_Dic_Trans:Get("&Content"):GetComponent("ContentFitterImmediate")

	---永遠顯示 的群組的位置
	this.m_Transform_MenuButtonGroup[EMenuGroup.Permanent] = this.m_ViewRef.m_Dic_Trans:Get("&Menu_GroupAlwaysShow")
	---時光機 的群組的位置
	this.m_Transform_MenuButtonGroup[EMenuGroup.TimeMachine] = this.m_ViewRef.m_Dic_Trans:Get("&Content_TimeMachine")
	---遊戲功能區_遊玩導向 的群組的位置
	this.m_Transform_MenuButtonGroup[EMenuGroup.GameArea_Play] = this.m_ViewRef.m_Dic_Trans:Get("&Content_GameArea_Play")
	---遊戲功能區_功能導向 的群組的位置
	this.m_Transform_MenuButtonGroup[EMenuGroup.GameArea_Functional] = this.m_ViewRef.m_Dic_Trans:Get("&Content_GameArea_Functional")
	---系統功能區_營運導向 的群組的位置
	this.m_Transform_MenuButtonGroup[EMenuGroup.SystemArea_Operations] = this.m_ViewRef.m_Dic_Trans:Get("&Content_System_Operations")
	---系統功能區_系統導向 的群組的位置
	this.m_Transform_MenuButtonGroup[EMenuGroup.SystemArea_Systems] = this.m_ViewRef.m_Dic_Trans:Get("&Content_System_Systems")

	---主要群組的位置
	this.m_GObj_GroupMain = this.m_ViewRef.m_Dic_Trans:Get("&Menu_GroupMain").gameObject
	---主按鈕的紅點
	this.m_Image_RedPoint_Main = this.m_ViewRef.m_Dic_Image:Get("&Image_RedPoint_Main")
	---紅點字串
	this.m_Text_RedPointCount_Menu = this.m_ViewRef.m_Dic_TMPText:Get("&Text_RedPointCount_Main")

    local _RedDotTable  = RedTableClass.NewData(this.m_Image_RedPoint_Main.gameObject, ERedDotUIIDTable.Menu_RightUPBtn, 0)
    RedDotMgr.AssignUICorrelation(ERedDotUIPos.Menu_AlwaysShow,_RedDotTable)


	---按鈕的參考_永遠顯示群組
	this.m_GObj_MenuButtonRef_Always = this.m_ViewRef.m_Dic_Trans:Get("&Button_MenuButton_Always").gameObject

	---按鈕的參考_Main群組
	this.m_GObj_MenuButtonRef_Other = this.m_ViewRef.m_Dic_Trans:Get("&Button_MenuButton_Main").gameObject

	---按鈕的參考_System群組
	this.m_GObj_MenuButtonRef_System = this.m_ViewRef.m_Dic_Trans:Get("&Button_MenuButton_System").gameObject

	---GameArea_Play 的 分隔線
	this.m_GObj_Divider_GameArea_Play = this.m_ViewRef.m_Dic_Trans:Get("&Divider_GameArea_Play").gameObject

	---GameArea_Functional 的 分隔線
	this.m_GObj_Divider_GameArea_Functional = this.m_ViewRef.m_Dic_Trans:Get("&Divider_GameArea_Functional").gameObject

	InitAllButtons()

	GStateObserverManager.Register(EStateObserver.PlayerLevelRefresh, this)
	GStateObserverManager.Register(EStateObserver.UpdateStaticFlag, this)
	this.m_GroupBtnActive = false
end

function Menu_Controller.Open(iParams)
	SetAllActive(false)
	if this.m_IsLoginFirstOpenMenu then
		this.UpdateAllButtonsState()
		this.m_IsLoginFirstOpenMenu = false
	end
	return true
end

function Menu_Controller.Close()
    SetAllActive(false)
    return true
end

---開啟其他 UI 後 主介面需要復原的東西
function Menu_Controller.ResetWhenOtherUIOpen()
	---檢查回到主介面後 Meun 要顯示的狀態
	if this.m_NeedOpenOthersWhenBackMainView then
		this.OnClick_OpenMenu()
	else
		SetAllActive(false)
		this.m_NeedOpenOthersWhenBackMainView = false
	end
	CheckMainButtonState()
end

function Menu_Controller.UIInMenu(iUI)
	return table.Contains(m_UIControllerInMenu, iUI)
end

function Menu_Controller.LoggingOutToDo()
	---登出 將m_NeedOpenOthersWhenBackMainView  恢復成跟預設相同
	this.m_NeedOpenOthersWhenBackMainView = false
	this.ResetWhenOtherUIOpen()
	this.m_IsLoginFirstOpenMenu = true
end

function Menu_Controller.OnDestroy()
	this.m_NeedOpenOthersWhenBackMainView = false
	table.Clear(this.m_MenuButtonData_StaticFlag)
	table.Clear(this.m_MenuButtonData_Level)
	table.Clear(this.m_MenuButtonData_Special)
	table.Clear(this.m_Transform_MenuButtonGroup)
	table.Clear(m_GroupAlways_Objects)
	table.Clear(m_UIControllerInMenu)
	table.Clear(m_MainButtonCircleRotate)

	for k,v in pairs(m_GroupSystem) do
		table.Clear(v)
	end
	
	for k,v in pairs(m_GroupMain_Objects) do
		table.Clear(v)
	end
	
	for k,v in pairs(m_VisableBatch) do
		UIVisibleEffect.RemoveUIVisibleBatch(v)
	end

    GStateObserverManager.UnRegister(EStateObserver.PlayerLevelRefresh, this)
	GStateObserverManager.UnRegister(EStateObserver.UpdateStaticFlag, this)

	return true
end

function Menu_Controller:OnStateChanged(iEStateObserver, ...)
	---參數
	local  _Params = {...}
	if iEStateObserver == EStateObserver.PlayerLevelRefresh then
		local _Lv = PlayerData.GetLv()
		if IsNeedUpdateButtonStateWithLevelUp(_Lv) then
			UpdateButtonStateByLevel(_Lv)
		end
	elseif iEStateObserver == EStateObserver.UpdateStaticFlag then
		local _StaticFlagID = _Params[1]
		if IsNeedUpdateButtonState(_StaticFlagID) then
			UpdateButtonStateByStaticFlag(_StaticFlagID)
		end
	end
end

---開關 MenuMain 區域
function Menu_Controller.OnClick_OpenMenu()
	if not this.m_GroupBtnActive then
		CheckDividerActive(this.m_Transform_MenuButtonGroup[EMenuGroup.GameArea_Play])
		CheckDividerActive(this.m_Transform_MenuButtonGroup[EMenuGroup.GameArea_Functional])
	end
	SetAllActive(not this.m_GroupBtnActive)
	
	this.m_NeedOpenOthersWhenBackMainView = false
	CheckMainButtonState()
end

---教學專用 開關 MenuMain 區域
function Menu_Controller.TeachOpenMenu()
	if not TeachMgr.IsTeaching() then
		return false
	end
	SetAllActive(true)
	this.m_NeedOpenOthersWhenBackMainView = false

	return true
end

---更新所有按鈕狀態
function Menu_Controller.UpdateAllButtonsState()
	this.UpdateAllButtonsByState(EMenuUpdateType.StaticFlag)
	this.UpdateAllButtonsByState(EMenuUpdateType.Level)
	this.UpdateAllButtonsByState(EMenuUpdateType.TimeMachineRoomActivated)
end

---更新所有按鈕狀態
---@param iUpdateType EMenuUpdateType 主選單更新方式
function Menu_Controller.UpdateAllButtonsByState(iUpdateType)
	--其他 或 預設 
	if iUpdateType == nil then
		iUpdateType = EMenuUpdateType.StaticFlag
	end
	--依照永標更新
	if iUpdateType == EMenuUpdateType.StaticFlag then
		for key, value in pairs(this.m_MenuButtonData_StaticFlag) do
			UpdateButtonStateByStaticFlag(key)
		end
	--依照等級更新
	elseif iUpdateType == EMenuUpdateType.Level then
		for key, value in pairs(this.m_MenuButtonData_Level) do
			UpdateButtonStateByLevel(key)
		end
	--依照特殊規則檢查
	--時光機艙室啟動檢查
	elseif iUpdateType == EMenuUpdateType.TimeMachineRoomActivated then
		UpdateButtonStateByGroupTimeMachine()
	end
end

---Update
function Menu_Controller.Update()
	---如果點擊左/右鍵 而且主要群組的介面是開啟的 要判斷位置並關閉介面
	if not TeachMgr.IsTeaching() and (Input.GetMouseButtonDown(0) or Input.GetMouseButtonDown(1) ) and 
		this.m_GObj_GroupMain.gameObject.activeSelf then
		local _TouchPoint = nil
		_TouchPoint = Input.mousePosition
		local _IsSuccess, _LocalPos =
            RectTransformUtility.ScreenPointToLocalPointInRectangle(this.m_Trans_ClickInMenuArea , _TouchPoint, UIMgr.m_UICamera, nil)
		if _IsSuccess then
			if this.m_Trans_ClickInMenuArea.rect:Contains(_LocalPos) == false then
				Button.OnPointerClick(this.m_GObj_BtnOpenMenu)
			end
		end
	end	
end

---顯示遊樂場按鍵 根據 m_ShowPlayGroundBtn 開或關
function Menu_Controller.ShowPlayGroundBtn()
	if this.m_Obj_PlayGroundBtn ~= nil then
		this.m_Obj_PlayGroundBtn:SetActive(this.m_ShowPlayGroundBtn)
	end
end

---常駐按鈕開/關互動
---@param iUIIndex number 要開關的按鈕 UIIndex
---@param iIsInteractable boolean 是否可互動
function Menu_Controller.SwitchPermanentButtonInteractable(iUIIndex, iIsInteractable)
    for _, value in pairs(this.m_MenuButtonData_Special[EMenuGroup.Permanent]) do
        local _IsTarget = value.m_UIIndex == iUIIndex
		local _IsSelect = _IsTarget and not iIsInteractable
        value.m_ButtonData.m_Button:SetSelect(_IsSelect)
        value.m_ButtonData.m_Button.m_ButtonEx.interactable = not _IsSelect
    end
end