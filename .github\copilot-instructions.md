# Unity/Lua 遊戲專案 AI 代理指令

## 專案架構

這是一個使用 Lua 進行遊戲邏輯開發的 Unity 遊戲專案。主要組件：

### 核心系統
- `Assets/04.LuaScript/Logic/` - 主要 Lua 邏輯檔案
- `Assets/03.RunTimeScript/` - Unity 執行時期腳本 (C#)
- `Assets/02.Art/` - 美術資源與素材
- `Assets/01.Scene/` - Unity 場景檔案
- `Assets/ToLua/` - Lua 綁定與整合框架

### UI 系統架構
UI 系統遵循控制器模式：

```lua
-- UI 控制器結構範例 (UIMgr.lua)
local _Stack_UIFullPage = Stack.New() -- 管理全版介面堆疊
UIBackGround_Controller -- 處理背景狀態
```

UI 重要概念：
- 全版介面使用堆疊管理 (`_Stack_UIFullPage`)
- UI 層級定義在 `EUIOrderLayers` (全版、左/右/中半版、頂層)
- 背景由 `UIBackGround_Controller` 管理

## 關鍵工作流程

### UI 開發
1. UI 控制器應放置於 `Assets/04.LuaScript/Logic/UI/`
2. 全版介面必須：
   - 設定 `m_UIOrderLayerKey = EUIOrderLayers.FullPage`
   - 如需自訂關閉動畫，實作 `OnCloseEffect`
3. 使用 `UIMgr` API 管理 UI 生命週期：
   ```lua
   UIMgr.Open(MyUI_Controller)  -- 開啟介面
   UIMgr.Close(MyUI_Controller) -- 關閉介面
   ```

### 資料管理
- 遊戲資料模型位於 `Assets/04.LuaScript/Logic/Data/`
- 使用 DataModel 模式進行結構化資料存取
- 參考實作：`DataModel.lua`

### UI 層級組織
```
EUIOrderLayers:
 FullPage (1000) - 全版介面
 Peak (3000) - 彈出視窗、覆蓋層
 Debug (5000) - 除錯/開發介面
```

## 常用模式

### UI 堆疊管理
```lua
-- 檢查頂層 UI 是否需要關閉效果
UIMgr.NeedWaitFullPageCloseEffect() -- 若頂層 UI 有 OnCloseEffect 則回傳 true

-- 處理 UI 切換
if _Stack_UIFullPage:Count() > 0 then
    local top = _Stack_UIFullPage:Peek()
    -- UI 切換邏輯
end
```

### 背景處理
- 使用 `UIBackGround_Controller.SetUIBackground_ForUIMgr()` 載入背景
- 僅在全版介面堆疊為空時隱藏背景
- 背景狀態與 UI 堆疊操作同步

## 關鍵整合點

1. Unity/Lua 橋接
   - ToLua 框架處理 Unity/Lua 整合
   - 詳見 `Assets/ToLua/` 的綁定細節

2. 資源管理
   - 資源檔案位於 `Assets/ResourcesBuild/`
   - Addressables 配置在 `Assets/AddressableAssetsData/`

如有問題或需要更新這些說明，請與團隊討論。
