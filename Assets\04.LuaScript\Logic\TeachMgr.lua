---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2021 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---教學管理器
---author 鼎翰
---version 1.0
---since [ProjectBase] 0.1
---date 2023.04.20
TeachMgr = {}
local this = TeachMgr

---是否有開啟教學 DebugLog
this.m_IsOpenTeachDebugLog = false

---是否正在教學中
local _IsTeaching = false

---此次教學所有步驟
local _Dic_Teach = {}

---此次教學的UI XXX_Controller
local _CurrentTeachUI = nil

---此次教學的步驟
local _StepIndexInTeach = 0

---教學完成之後事件 ID
local _NextEventID = 0

--region Local Function

local function GetCurrentStepData(iIndex)
    return _Dic_Teach[iIndex].m_TeachStepData
end

---設定教學資料
---@param i_Table_TeachData table 教學資料步驟
local function SetData(i_Table_TeachData)
    for i = 1, table.Count(i_Table_TeachData) do
        _Dic_Teach[i] = {}
        _Dic_Teach[i].m_IsTeachStepSetupCompleted = false
        _Dic_Teach[i].m_TeachStepData= i_Table_TeachData[i]
    end
end

---停止教學
local function StopTeach()
    --檢查此次教學的所有界面
    local _Table_CurrentUI = {}
    for key, value in pairs(_Dic_Teach) do
        --此次教學的界面
        local _CurrentUI = UISetting.GetUIControllerByUIIndex(value.m_TeachStepData.m_UIIndex)
        if _CurrentUI and not table.Contains(_Table_CurrentUI, _CurrentUI) then
            table.insert(_Table_CurrentUI, _CurrentUI)
        end
    end

    --檢查有沒有 CoroutineHelper 有的話教學不需要了就把他刪了
    for key, value in pairs(_Table_CurrentUI) do
        if value.m_CoroutineHelper ~= nil and not value.m_CoroutineHelper.IsNeedKeepInUI then
            GameObject.Destroy(value.m_CoroutineHelper)
            value.m_CoroutineHelper = nil
        end
    end

    _Dic_Teach = {}
    _IsTeaching = false
    _CurrentTeachUI = nil
    _StepIndexInTeach = 0
    UIMgr.Close(Teach_Controller)
    if table.Contains(UIMgr.m_Table_UIOpenByEvent, Teach_Controller) then
        SendProtocol_006._006()
    end
    ---檢查 教學後 有沒有事件要觸發
    if _NextEventID > 0 then
        SendProtocol_006._017(_NextEventID)
        _NextEventID = 0
    end
end

---撥放教學
local function Play()
    --進入下一步 如果是第一步 也要++ 因為初始化為0 0沒東西
    _StepIndexInTeach = _StepIndexInTeach + 1
    --檢查是否有下一步 或者 要開啟其它的介面
    if _StepIndexInTeach > table.Count(_Dic_Teach) then

        local _LastIndex = table.Count(_Dic_Teach)

        local _CurrentStepData = GetCurrentStepData(_LastIndex)

        if _CurrentStepData.m_ETeachType == ETeachType.RawImage or
        _CurrentStepData.m_ETeachType == ETeachType.TypingMeching then

            ---沒有下一步檢查是否有要開啟的介面 有介面開介面
            if _CurrentStepData.m_OpenUIID ~= nil and _CurrentStepData.m_OpenUIID ~= 0 then
            
                local _UIController = UISetting.GetUIControllerByUIIndex(_CurrentStepData.m_OpenUIID)
                if _UIController ~= nil then
                    UIMgr.Open(_UIController)
                else
                    TeachMgr.TeachLog(2,"[TeachMgr] 找不到對應 UIController。 TeachStepDataID:" .. _LastIndex.."，OpenUIID：".. _CurrentStepData.m_OpenUIID)
                end
            end
        end
        
        ---不管有沒有要開的介面都要結束教學關閉畫面
        StopTeach()
        return
    end
    Teach_Controller.RenewContent()

    ---要求S端加永標 不確定是否每個教學都有永標 做0值判斷 
    
    if GetCurrentStepData(_StepIndexInTeach).m_FlagID ~=0 then
        SendProtocol_002._004(GetCurrentStepData(_StepIndexInTeach).m_FlagID)
    end
end

---開始教學
---@param iUI table UI 本人 XXX_Controller
---@param iTeachStepDataStartID table 此次教學的步驟資料 ID
local function StartTeach(iTeachingStepsData)
    SetData(iTeachingStepsData)
    --檢查 是否已完成過此教學
    _CurrentTeachUI = UISetting.GetUIControllerByUIIndex(iTeachingStepsData[1].m_UIIndex)
    --開始播放教學
    _IsTeaching = true
    Play()
end

--endregion

---初始化
function TeachMgr.Init()
    _Dic_Teach = {}
    _IsTeaching = false
    _CurrentTeachUI = nil
    _StepIndexInTeach = 0
end

---製作教學元件資料
function TeachMgr.NewTeachComponent(iUI, iUITeachComponent)
    ---教學元件資料
    local _CurrentComponentData = {}
    ---教學元件名稱
    ---@type string
    _CurrentComponentData.m_Name = iUITeachComponent.gameObject.name
    ---教學元件類型
    ---@type string
    _CurrentComponentData.m_ETeachComponentType = iUITeachComponent.ETeachComponentType
    ---教學元件 MaskButton 的圖片
    ---@type Image
    _CurrentComponentData.m_Image_TeachComponent = iUITeachComponent.Image_TeachComponent
    ---教學元件使用圖片的長寬來當 FocusButton 的長寬 [ =false 的話就會直接拿元件的長寬 ]
    ---@type boolean
    _CurrentComponentData.m_IsUseImageWidthAndHeight = iUITeachComponent.IsUseImageWidthAndHeight
    ---自己或父物件的教學的動態
    ---@type boolean
    _CurrentComponentData.m_UIAnimation = iUITeachComponent.UIAnimation
    ---教學元件 本人
    _CurrentComponentData.m_TeachComponent = nil

    ---教學元件 類型
    ---@type ETeachComponentType
    local _ETeachComponentType = _CurrentComponentData.m_ETeachComponentType
    ---要取得的 Type
    local _Type = nil
    --依照類型取得教學元件 本人 (ETeachComponentType.Button_NextStep 和 ETeachComponentType.FullPageButton_NextStep 不用設定)
    if _ETeachComponentType == table.GetKey(ETeachComponentType, ETeachComponentType.Default) then
        --取按鈕
        _Type = typeof(RectTransform)
    elseif _ETeachComponentType == table.GetKey(ETeachComponentType, ETeachComponentType.Button) then
        --取按鈕
        _Type = typeof(ButtonEx)
    elseif _ETeachComponentType == table.GetKey(ETeachComponentType, ETeachComponentType.Toggle) then
        --取 Toggle
        _Type = typeof(Toggle)
    elseif _ETeachComponentType == table.GetKey(ETeachComponentType, ETeachComponentType.Slider) then
        --取 Slider
        _Type = typeof(Slider)
    elseif _ETeachComponentType == table.GetKey(ETeachComponentType, ETeachComponentType.DynamicIcon) then
        --取 ItemIcon
        _Type = typeof(ButtonEx)
    elseif _ETeachComponentType == table.GetKey(ETeachComponentType, ETeachComponentType.FullPageButton) then
        --取 ItemIcon
        _Type = typeof(ButtonEx)
    end

    --依照設定取元件
    if iUITeachComponent.IsTeachComponentInChildren then
        _CurrentComponentData.m_TeachComponent = iUITeachComponent.transform:GetComponentInChildren(_Type)
    else
        _CurrentComponentData.m_TeachComponent = iUITeachComponent.gameObject:GetComponent(_Type)
    end

    if _CurrentComponentData.m_TeachComponent == nil then
        _CurrentComponentData.m_TeachComponent = iUITeachComponent.gameObject
    else
        --設定 OnPointerClick 事件 (ETeachComponentType.Button_NextStep 和 ETeachComponentType.FullPageButton_NextStep 不用設定)
        _CurrentComponentData.m_FunctionOnPointerClick = function()
            --依照類型設定事件
            
            if _ETeachComponentType == table.GetKey(ETeachComponentType, ETeachComponentType.Default) then

            elseif _ETeachComponentType == table.GetKey(ETeachComponentType, ETeachComponentType.Button) then
                Button.OnPointerClick(_CurrentComponentData.m_TeachComponent)
            elseif _ETeachComponentType == table.GetKey(ETeachComponentType, ETeachComponentType.Toggle) then
                --如果是 Toggle
                _CurrentComponentData.m_TeachComponent.isOn = not _CurrentComponentData.m_TeachComponent.isOn
            elseif _ETeachComponentType == table.GetKey(ETeachComponentType, ETeachComponentType.Slider) then
                --如果是 Slider
            elseif _ETeachComponentType == table.GetKey(ETeachComponentType, ETeachComponentType.DynamicIcon) then
                Button.OnPointerClick(_CurrentComponentData.m_TeachComponent)
            elseif _ETeachComponentType == table.GetKey(ETeachComponentType, ETeachComponentType.FullPageButton) then
                Button.OnPointerClick(_CurrentComponentData.m_TeachComponent)
            end
        end
    end

    --加入紀錄
    iUI.m_Table_TeachComponent[_CurrentComponentData.m_Name] = _CurrentComponentData

    return _CurrentComponentData
end

---此 UI 的教學元件初始化
function TeachMgr.InitTeachComponentData(iUI)
    ---此 UI 上的所有教學元件
    local _TableUITeachComponent = iUI.m_ViewRef:GetComponentsInChildren(typeof(UITeachComponent), true):ToTable()
    ---此 UI 上的所有的 LeanTweenVisual
    iUI.m_Table_LeanTweenVisual = iUI.m_ViewRef:GetComponentsInChildren(typeof(LeanTweenVisual), true):ToTable()
    --製作教學元件資料
    if table.Count(_TableUITeachComponent) > 0 then
        for key, value in pairs(_TableUITeachComponent) do
            TeachMgr.NewTeachComponent(iUI, value)
        end
        TeachMgr.TeachLog(1, iUI.m_Name.."教學元件初始化完成")
    end
end

---檢查教學步驟資料是否可以開始
---@param iTeachStepDataStartID number 教學起始步驟 ID
---@param iIsNeedCheckFlag bool 是否需要檢查永標 重複教學用
---@param iNextEventID uint32 下一步的事件 ID
function TeachMgr.CheckCanTeachAndStartTeach(iTeachStepDataStartID, iIsNeedCheckFlag, iNextEventID)
    --教學中 跳掉
    if TeachMgr.IsTeaching() then
		TeachMgr.TeachLog(2,"[TeachMgr] 正在教學中，無法開啟新手教學: TeachID:" .. iTeachStepDataStartID)
		return false
	end

    ---此次教學的步驟資料
	local _TeachingStepsData = TeachingStepsData.Get(iTeachStepDataStartID)

    if _TeachingStepsData ~= nil then
        --檢查 UI 是否有教學資料
        if table.Count(_TeachingStepsData) <= 0 then
            --沒有教學資料
            TeachMgr.TeachLog(3,"[TeachMgr] 找不到此教學資料起始步驟 ID："..iTeachStepDataStartID.." 的相關教學資料，請確認")
            StopTeach()
            return false
        else
            ---檢查是否有開啟過教學
            if iIsNeedCheckFlag then
                if PlayerData.GetFlags(EFlags.Static).IsHaveFlag(_TeachingStepsData[1].m_FlagID) then
                    --完成過
                    TeachMgr.TeachLog(3,"[TeachMgr] 已完成過此教學 教學資料起始步驟 ID："..iTeachStepDataStartID)
                    StopTeach()
                    return false
                end
            end
        end
        --檢查都通過 開始教學
        if iNextEventID == nil then
            iNextEventID = 0
        end
        _NextEventID = iNextEventID
        StartTeach(_TeachingStepsData)
        TeachMgr.TeachLog(1,"<color=#FFEB04>[TeachMgr] 開始教學 教學資料起始步驟 ID："..iTeachStepDataStartID.."</color>")
        return true
    else
        TeachMgr.TeachLog(3,"[TeachMgr] 找不到此教學資料起始步驟 ID："..iTeachStepDataStartID.."，請確認")
        StopTeach()
        return false
    end
end

---下一步教學
function TeachMgr.NextStep()
    if _Dic_Teach[_StepIndexInTeach] ~= nil and _Dic_Teach[_StepIndexInTeach].m_IsTeachStepSetupCompleted == true then
        Teach_Controller.StopVocal()
        Play()
    end
end

---結束教學
function TeachMgr.StopTeach()
    Teach_Controller.StopVocal()
    StopTeach()
end

---是否正在教學中
function TeachMgr.IsTeaching()
    return _IsTeaching == true
end

---是否是最後一個教學
function TeachMgr.IsLastTeaching()
    return _StepIndexInTeach == table.Count(_Dic_Teach)
end

---取得 此次教學目前所有的教學步驟資料
function TeachMgr.GetCurrentAllTeachStepData()
    return _Dic_Teach
end

---取得 現在要進行的教學步驟資料
function TeachMgr.GetCurrentTeachStepData()
    return GetCurrentStepData(_StepIndexInTeach)
end

function TeachMgr.SetCurrentTeachStepSetupCompleted()
    if _Dic_Teach[_StepIndexInTeach] ~= nil then
        _Dic_Teach[_StepIndexInTeach].m_IsTeachStepSetupCompleted = true
    end
end

---取得 現在要進行的教學步驟的介面
function TeachMgr.GetCurrentTeachStepUI()
    return _CurrentTeachUI
end

---檢查當前使用到的教學是否為步驟中的第一步
function TeachMgr.GetIsFirstStep()
    return (_StepIndexInTeach <= 1)
end

---檢查 FocusStep 教學目標的 UI 是否開啟
---@return boolean
function TeachMgr.FocusStepCheckUIIsOpen(iCurrentUI)
    --UI 有特殊的開關方式才要特別處理
    if iCurrentUI == Menu_Controller then
        --Menu 原本就是開的 只是控制 特定區域的 Visible
        return Menu_Controller.TeachOpenMenu()
    end

    --region 如果目標UI是Main_Controller則關閉半版避免被遮住 Modify by 凌傑RM#117450 2025.0107
    if iCurrentUI == Main_Controller then
        UIMgr.CloseHalfPageByMain()
    end
    --endregion

    --先檢查此步驟的 UI 有沒有開啟
    if not UIMgr.IsVisible(iCurrentUI) then
        return UIMgr.Open(iCurrentUI)
    end
    return true
end

---教學 Log 
---金手指有開啟才會顯示
---@param iLogType int 要顯示訊息的類型 ( 1一般 2警告 3錯誤 )
---@param iString string 要顯示的訊息
function TeachMgr.TeachLog(iLogType,iString)
    if ProjectMgr.IsDebug() then
        --TeachDebug 沒開 就不顯示
        if this.m_IsOpenTeachDebugLog then
            if iLogType == 1 then
                D.Log(iString)
            elseif iLogType == 2 then
                D.LogWarning(iString)
            elseif iLogType == 3 then
                D.LogError(iString)
            end
        end
    end
end

return TeachMgr
