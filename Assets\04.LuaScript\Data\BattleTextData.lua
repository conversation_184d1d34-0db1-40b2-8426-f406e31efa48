---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---@class BattleTextData
---author Were
---version 1.0
---since [ProjectBase] 0.1
---date 2023.3.7

BattleTextData = {}
BattleTextData.__index = BattleTextData

local this = BattleTextData

--- 此串檔是否已經初始化完成
this.m_IsDataInitialized = false

-- local json = require "cjson"

local ASSET_NAME = "BattleTextStyleData"
local ASSET_LOCATION = "./Assets/ResourcesBuild/BattleTextStyle/"
local DATA_TYPE = ".json"
local m_Dic = {}

---初始化
function BattleTextData.Init()
    this.m_IsDataInitialized = true
    DataMgr.AddInitializedDataCount()
    return JsonMgr.LoadTableFromResources(ASSET_NAME, BattleTextData.OnLoadData)
end

---檔案載入完畢執行
function BattleTextData.OnLoadData(iFile)
    m_Dic = iFile
end

---取得全部資料
function BattleTextData.GetAllData()
    return m_Dic
end


---取得特定資料
---@param iEBattleTextType EBattleTextType 戰鬥數值類型
function BattleTextData.GetData(iEBattleTextType)
    return m_Dic[tostring( iEBattleTextType)]
end

---將目前的儲存資料存回Json
function BattleTextData.SaveJsonData()

    local _assetName = ASSET_NAME..DATA_TYPE
    JsonMgr.saveTable(m_Dic ,_assetName ,ASSET_LOCATION)
end

--[[
    Json結構
  EBattleTextType : {
    "m_Size_Font": 10,          // 預設文字大小
    "m_Time_Show": 0.5,         // 演出時間
    "m_Time_Fadeout_Delay": 0.5,// 淡出延遲時間
    "m_Time_Fadeout": 0.5,      // 淡出時間
    "m_Font_Type": "BattleText",// 使用字體 "BattleText"/"BigTitle"
    "m_Font_Spacing": -50,      // 字型間距
    "m_Scale_Start": 1,         // 初始預設縮放值
    "m_Scale_End": 1,           // 結束動畫縮放值
    "m_Ay_Color": [             // 使用漸層顏色
      "#FFFFFF",
      "#FFFFFF",
      "#6a6a6a",
      "#6a6a6a"
    ],

    "m_UseEase": 27,            // 演出軌跡 ELeanTweenType
    "m_Pos_Start": {            // 初始位置
      "x": 0,
      "y": 0
    },
    "m_Pos_End": {              // 位移量
      "x": 0,
      "y": 0
    },
    "m_Pos_Fade": {              // 結束位移量
      "x": 0,
      "y": 0
    },
    "m_Pos_OffsetA": {          // 位置微調 A
      "x": 0,
      "y": 0
    },
    "m_Pos_OffsetB": {          // 位置微調 B
      "x": 0,
      "y": 0
    },
    "m_IconName": "",           // 是否使用Icon
    "m_Icon_Size": {            // Icon大小
      "x": 18,
      "y": 18
    },
    "m_Icon_Alpha": 1,           // Icon透明度

    "m_Size_Bonus" : 0,          // 追加字大小(未填則無)

    "m_Pos_Bonus" :              // 追加字位置
    {
      "x": 0,
      "y": 0
    }
  }

--]]

