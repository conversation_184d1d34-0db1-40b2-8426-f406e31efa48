//=====================================================================
//              CHINESE GAMER PROPRIETARY INFORMATION
//
// This software is supplied under the terms of a license agreement or
// nondisclosure agreement with CHINESE GAMER and may not 
// be copied or disclosed except in accordance with the terms of that
// agreement.
//
//                 Copyright © 2025 by CHINESE GAMER.
//                      All Rights Reserved.
//
//    -------------------------------------------------------------    
//
//=====================================================================

using DentedPixel.LTEditor;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

/// <summary>
/// 動畫序列管理器
/// <AUTHOR>
/// @telephone 2917
/// @version 1.0
/// @since 黃易群俠傳M
/// @date 2025.8.12
/// </summary>
public class UIAnimationScheduler : MonoBehaviour
{
    #region Constants
    private const float DEFAULT_DELAY_TIME = 0.1f;
    private const int MAX_SEQUENCE_COUNT = 100;
    #endregion

    #region Enums
    /// <summary>
    /// 序列項目類型
    /// </summary>
    public enum SequenceItemType
    {
        LeanTweenVisual    // LeanTweenVisual 組件
    }

    /// <summary>
    /// LeanTween 動作類型
    /// </summary>
    private enum LeanTweenAction
    {
        MOVE_X,
        MOVE_Y,
        MOVE_Z,
        MOVE_LOCAL_X,
        MOVE_LOCAL_Y,
        MOVE_LOCAL_Z,
        ROTATE_X,
        ROTATE_Y,
        ROTATE_Z,
        ROTATE_AROUND,
        ROTATE_LOCAL_X,
        ROTATE_LOCAL_Y,
        ROTATE_LOCAL_Z,
        SCALE_X,
        SCALE_Y,
        SCALE_Z,
        ALPHA
    }

    /// <summary>
    /// 序列播放狀態
    /// </summary>
    public enum SequenceState
    {
        Stopped,    // 停止
        Playing,    // 播放中
        Paused      // 暫停
    }
    #endregion

    #region Serializable Classes
    /// <summary>
    /// 序列項目資料結構
    /// </summary>
    [System.Serializable]
    public class SequenceItem
    {
        [Header( "基本設定" )]
        /// <summary>項目名稱</summary>
        public string m_Name = "Sequence Item";

        /// <summary>序列項目類型</summary>
        public SequenceItemType m_ItemType = SequenceItemType.LeanTweenVisual;

        /// <summary>是否啟用此項目</summary>
        public bool m_IsEnabled = true;

        /// <summary>此項目開始播放前的延遲秒數</summary>
        public float m_DelayTime = 0.0f;

        [Header( "LeanTweenVisual 設定" )]
        /// <summary>目標 LeanTweenVisual 組件</summary>
        public LeanTweenVisual m_LeanTweenVisual;

        /// <summary>此動畫播放完成後才會播放下個動畫</summary>
        public bool m_WaitForCompletion = true;
    }
    #endregion

    #region Classes
    /// <summary>
    /// 儲存物件的初始值
    /// </summary>
    private class InitialValues
    {
        private GameObject m_Target;
        private RectTransform m_RectTransform;
        private CanvasGroup m_CanvasGroup;
        private SpriteRenderer m_SpriteRenderer;
        private UnityEngine.UI.Image m_Image;

        private bool m_HasAlpha = false;
        private bool m_HasPosition = false;
        private bool m_HasScale = false;
        private bool m_HasRotation = false;
        private bool m_HasColor = false;

        private Vector3 m_Position;
        private Vector3 m_LocalPosition;
        private Vector3 m_Rotation;
        private Vector3 m_LocalRotation;
        private Vector3 m_Scale;
        private float m_Alpha;
        private Color m_Color;

        public InitialValues(GameObject iTarget)
        {
            m_Target = iTarget;
            m_RectTransform = iTarget.GetComponent<RectTransform>();
            m_CanvasGroup = iTarget.GetComponent<CanvasGroup>();
            m_SpriteRenderer = iTarget.GetComponent<SpriteRenderer>();
            m_Image = iTarget.GetComponent<UnityEngine.UI.Image>();
        }

        public void SetFromValue(DentedPixel.LTEditor.LeanTweenItem iTweenData)
        {
            var action = iTweenData.actionStr ?? "";

            if (action.Contains("MOVE"))
            {
                m_HasPosition = true;
                bool isLocal = action.Contains("LOCAL");
                Vector3 position = iTweenData.from;

                // Handle axis-specific moves
                if (action.Contains("_X") || action.Contains("_Y") || action.Contains("_Z"))
                {
                    Vector3 currentPos;
                    if (m_RectTransform != null)
                        currentPos = m_RectTransform.anchoredPosition3D;
                    else
                        currentPos = isLocal ? m_Target.transform.localPosition : m_Target.transform.position;

                    if (action.Contains("_X"))
                        position = new Vector3(iTweenData.from.x, currentPos.y, currentPos.z);
                    else if (action.Contains("_Y"))
                        position = new Vector3(currentPos.x, iTweenData.from.y, currentPos.z);
                    else if (action.Contains("_Z"))
                        position = new Vector3(currentPos.x, currentPos.y, iTweenData.from.z);
                }

                if (isLocal || m_RectTransform != null)
                    m_LocalPosition = position;
                else
                    m_Position = position;
            }
            else if (action.Contains("SCALE"))
            {
                m_HasScale = true;
                Vector3 scale = iTweenData.from;

                if (action.Contains("_X") || action.Contains("_Y") || action.Contains("_Z"))
                {
                    Vector3 currentScale = m_Target.transform.localScale;
                    if (action.Contains("_X"))
                        scale = new Vector3(iTweenData.from.x, currentScale.y, currentScale.z);
                    else if (action.Contains("_Y"))
                        scale = new Vector3(currentScale.x, iTweenData.from.y, currentScale.z);
                    else if (action.Contains("_Z"))
                        scale = new Vector3(currentScale.x, currentScale.y, iTweenData.from.z);
                }

                m_Scale = scale;
            }
            else if (action.Contains("ROTATE"))
            {
                m_HasRotation = true;
                bool isLocal = action.Contains("LOCAL");
                Vector3 rotation = iTweenData.from;

                if (action.Contains("_X") || action.Contains("_Y") || action.Contains("_Z"))
                {
                    Vector3 currentRot = isLocal ? m_Target.transform.localEulerAngles : m_Target.transform.eulerAngles;
                    if (action.Contains("_X"))
                        rotation = new Vector3(iTweenData.from.x, currentRot.y, currentRot.z);
                    else if (action.Contains("_Y"))
                        rotation = new Vector3(currentRot.x, iTweenData.from.y, currentRot.z);
                    else if (action.Contains("_Z"))
                        rotation = new Vector3(currentRot.x, currentRot.y, iTweenData.from.z);
                }

                if (isLocal)
                    m_LocalRotation = rotation;
                else
                    m_Rotation = rotation;
            }
            else if (action.Contains("ALPHA"))
            {
                m_HasAlpha = true;
                m_Alpha = iTweenData.from.x;
            }
            else if (action.Contains("COLOR"))
            {
                m_HasColor = true;
                m_Color = iTweenData.colorTo;
            }
        }

        public void Apply()
        {
            if (m_HasPosition)
            {
                if (m_RectTransform != null)
                    m_RectTransform.anchoredPosition3D = m_LocalPosition;
                else
                {
                    if (m_LocalPosition != Vector3.zero)
                        m_Target.transform.localPosition = m_LocalPosition;
                    if (m_Position != Vector3.zero)
                        m_Target.transform.position = m_Position;
                }
            }

            if (m_HasScale && m_Scale != Vector3.zero)
                m_Target.transform.localScale = m_Scale;

            if (m_HasRotation)
            {
                if (m_LocalRotation != Vector3.zero)
                    m_Target.transform.localEulerAngles = m_LocalRotation;
                else if (m_Rotation != Vector3.zero)
                    m_Target.transform.eulerAngles = m_Rotation;
            }

            if (m_HasAlpha)
            {
                if (m_CanvasGroup != null)
                    m_CanvasGroup.alpha = m_Alpha;
                else if (m_SpriteRenderer != null)
                {
                    Color color = m_SpriteRenderer.color;
                    color.a = m_Alpha;
                    m_SpriteRenderer.color = color;
                }
                else if (m_Image != null)
                {
                    Color color = m_Image.color;
                    color.a = m_Alpha;
                    m_Image.color = color;
                }
            }

            if (m_HasColor)
            {
                if (m_SpriteRenderer != null)
                    m_SpriteRenderer.color = m_Color;
                else if (m_Image != null)
                    m_Image.color = m_Color;
            }
        }
    }
    #endregion

    #region Member Variables
    [Header( "序列設定" )]
    /// <summary>序列項目清單</summary>
    public List<SequenceItem> m_SequenceItems = new List<SequenceItem>();

    [Header( "播放設定" )]
    /// <summary>是否啟動時自動播放</summary>
    public bool m_IsPlayOnStart = false;

    /// <summary>是否循環播放</summary>
    public bool m_IsLoop = false;

    /// <summary>循環次數 (-1 為無限循環)</summary>
    public int m_LoopCount = -1;

    [Header( "除錯設定" )]
    /// <summary>是否顯示除錯資訊</summary>
    public bool m_ShowDebugInfo = false;

    /// <summary>當前播放狀態</summary>
    private SequenceState m_CurrentState = SequenceState.Stopped;

    /// <summary>當前播放索引</summary>
    private int m_CurrentIndex = 0;

    /// <summary>當前循環次數</summary>
    private int m_CurrentLoopCount = 0;

    /// <summary>當前序列 ID</summary>
    private int m_CurrentSequenceId = -1;

    /// <summary>序列完成回調</summary>
    private System.Action m_OnSequenceComplete;

    /// <summary>序列項目完成回調</summary>
    private System.Action<int> m_OnItemComplete;
    #endregion

    #region Events
    [Header( "事件" )]
    /// <summary>序列開始事件</summary>
    public UnityEvent m_OnSequenceStart;

    /// <summary>序列完成事件</summary>
    public UnityEvent m_OnSequenceFinished;

    /// <summary>序列項目開始事件</summary>
    public UnityEvent<int> m_OnItemStart;

    /// <summary>序列項目完成事件</summary>
    public UnityEvent<int> m_OnItemFinished;
    #endregion

    #region Properties
    /// <summary>
    /// 當前播放狀態
    /// </summary>
    public SequenceState CurrentState => m_CurrentState;

    /// <summary>
    /// 當前播放索引
    /// </summary>
    public int CurrentIndex => m_CurrentIndex;

    /// <summary>
    /// 序列項目總數
    /// </summary>
    public int ItemCount => m_SequenceItems?.Count ?? 0;

    /// <summary>
    /// 是否正在播放
    /// </summary>
    public bool IsPlaying => m_CurrentState == SequenceState.Playing;

    /// <summary>
    /// 是否已停止
    /// </summary>
    public bool IsStopped => m_CurrentState == SequenceState.Stopped;

    /// <summary>
    /// 是否已暫停
    /// </summary>
    public bool IsPaused => m_CurrentState == SequenceState.Paused;
    #endregion

    #region Unity Lifecycle
    /// <summary>
    /// Unity Start 事件
    /// </summary>
    private void Start()
    {
        if( m_IsPlayOnStart )
        {
            PlaySequence();
        }
    }

    /// <summary>
    /// Unity OnDestroy 事件
    /// </summary>
    private void OnDestroy()
    {
        StopSequence();
    }
    #endregion

    #region Public Methods
    /// <summary>
    /// 播放序列
    /// </summary>
    /// <param name="iFromIndex">從指定索引開始播放</param>
    public void PlaySequence( int iFromIndex = 0 )
    {
        if( m_SequenceItems == null || m_SequenceItems.Count == 0 )
        {
            LogDebug( "序列項目清單為空，無法播放" );
            return;
        }

        if( m_CurrentState == SequenceState.Playing )
        {
            LogDebug( "序列已在播放中" );
            return;
        }

        m_CurrentIndex = Mathf.Clamp( iFromIndex, 0, m_SequenceItems.Count - 1 );
        m_CurrentState = SequenceState.Playing;
        m_CurrentLoopCount = 0;

        LogDebug( $"開始播放序列，從索引 {m_CurrentIndex} 開始" );

        m_OnSequenceStart?.Invoke();
        PlayNextItem();
    }

    /// <summary>
    /// 停止序列播放
    /// </summary>
    public void StopSequence()
    {
        if( m_CurrentState == SequenceState.Stopped )
        {
            return;
        }

        LogDebug( "停止序列播放" );

        // 取消當前的 LeanTween
        if( m_CurrentSequenceId != -1 )
        {
            LeanTween.cancel( m_CurrentSequenceId );
            m_CurrentSequenceId = -1;
        }

        // 停止當前播放的 LeanTweenVisual
        if( m_CurrentIndex >= 0 && m_CurrentIndex < m_SequenceItems.Count )
        {
            var _CurrentItem = m_SequenceItems[ m_CurrentIndex ];
            if( _CurrentItem.m_LeanTweenVisual != null )
            {
                LeanTween.cancel( _CurrentItem.m_LeanTweenVisual.gameObject );
            }
        }

        m_CurrentState = SequenceState.Stopped;
        m_CurrentIndex = 0;
        m_CurrentLoopCount = 0;
    }

    /// <summary>
    /// 暫停序列播放
    /// </summary>
    public void PauseSequence()
    {
        if( m_CurrentState == SequenceState.Playing )
        {
            LogDebug( "暫停序列播放" );
            m_CurrentState = SequenceState.Paused;

            // 暫停當前的 LeanTween
            if( m_CurrentSequenceId != -1 )
            {
                LeanTween.pause( m_CurrentSequenceId );
            }
        }
    }

    /// <summary>
    /// 恢復序列播放
    /// </summary>
    public void ResumeSequence()
    {
        if( m_CurrentState == SequenceState.Paused )
        {
            LogDebug( "恢復序列播放" );
            m_CurrentState = SequenceState.Playing;

            // 恢復當前的 LeanTween
            if( m_CurrentSequenceId != -1 )
            {
                LeanTween.resume( m_CurrentSequenceId );
            }
        }
    }

    /// <summary>
    /// 跳到指定索引並播放
    /// </summary>
    /// <param name="iIndex">目標索引</param>
    public void JumpToIndex( int iIndex )
    {
        if( iIndex < 0 || iIndex >= m_SequenceItems.Count )
        {
            LogDebug( $"索引 {iIndex} 超出範圍" );
            return;
        }

        StopSequence();
        PlaySequence( iIndex );
    }

    /// <summary>
    /// 播放下一個項目
    /// </summary>
    public void PlayNext()
    {
        if( m_CurrentState != SequenceState.Playing )
        {
            return;
        }

        m_CurrentIndex++;
        if( m_CurrentIndex >= m_SequenceItems.Count )
        {
            OnSequenceComplete();
        }
        else
        {
            PlayNextItem();
        }
    }

    /// <summary>
    /// 播放上一個項目
    /// </summary>
    public void PlayPrevious()
    {
        if( m_CurrentIndex > 0 )
        {
            JumpToIndex( m_CurrentIndex - 1 );
        }
    }
    #endregion

    #region Private Methods
    /// <summary>
    /// 播放下一個項目
    /// </summary>
    private void PlayNextItem()
    {
        if( m_CurrentState != SequenceState.Playing || m_CurrentIndex >= m_SequenceItems.Count )
        {
            return;
        }

        var _CurrentItem = m_SequenceItems[ m_CurrentIndex ];

        if( !_CurrentItem.m_IsEnabled )
        {
            LogDebug( $"跳過已停用的項目 [{m_CurrentIndex}]: {_CurrentItem.m_Name}" );
            PlayNext();
            return;
        }

        LogDebug( $"準備播放項目 [{m_CurrentIndex}]: {_CurrentItem.m_Name} (類型: {_CurrentItem.m_ItemType})" );

        // 處理延遲時間
        if( _CurrentItem.m_DelayTime > 0 )
        {
            LogDebug( $"等待 {_CurrentItem.m_DelayTime} 秒後開始播放項目: {_CurrentItem.m_Name}" );
            m_CurrentSequenceId = LeanTween.delayedCall( _CurrentItem.m_DelayTime, () => {
                ExecuteItemPlay( _CurrentItem );
            } ).id;
            return;
        }

        ExecuteItemPlay( _CurrentItem );
    }

    /// <summary>
    /// 執行項目播放
    /// </summary>
    /// <param name="iItem">序列項目</param>
    private void ExecuteItemPlay( SequenceItem iItem )
    {
        m_OnItemStart?.Invoke( m_CurrentIndex );
        
        LogDebug( $"開始播放項目: {iItem.m_Name}" );

        switch( iItem.m_ItemType )
        {
            case SequenceItemType.LeanTweenVisual:
                PlayLeanTweenVisualItem( iItem );
                break;

            default:
                LogDebug( $"未知的項目類型: {iItem.m_ItemType}" );
                PlayNext();
                break;
        }
    }

    /// <summary>
    /// 播放 LeanTweenVisual 項目
    /// </summary>
    /// <param name="iItem">序列項目</param>
    private void PlayLeanTweenVisualItem( SequenceItem iItem )
    {
        if( iItem.m_LeanTweenVisual == null )
        {
            LogDebug( $"LeanTweenVisual 組件為空，跳過項目: {iItem.m_Name}" );
            PlayNext();
            return;
        }

        // 設定初始值
        SetupInitialValues( iItem.m_LeanTweenVisual );

        // 啟動 LeanTweenVisual 動畫
        iItem.m_LeanTweenVisual.buildAllTweens( false );

        if( iItem.m_WaitForCompletion )
        {
            // 計算動畫總時間
            float _TotalDuration = CalculateLeanTweenVisualDuration( iItem.m_LeanTweenVisual );

            if( _TotalDuration > 0 )
            {
                // 等待動畫完成
                m_CurrentSequenceId = LeanTween.delayedCall( _TotalDuration, () => {
                    OnItemComplete();
                } ).id;
            }
            else
            {
                // 如果無法計算時間，立即播放下一個
                PlayNext();
            }
        }
        else
        {
            // 不等待完成，立即播放下一個
            PlayNext();
        }
    }

    /// <summary>
    /// 設定 LeanTweenVisual 的初始值
    /// </summary>
    /// <param name="iLeanTweenVisual">目標 LeanTweenVisual 組件</param>
    private void SetupInitialValues( LeanTweenVisual iLeanTweenVisual )
    {
        if( iLeanTweenVisual == null || iLeanTweenVisual.groupList == null )
            return;

        // 暫存所有初始值
        Dictionary<GameObject, InitialValues> _InitialValuesMap = new Dictionary<GameObject, InitialValues>();

        // 遍歷所有組並收集初始值
        foreach( var _Group in iLeanTweenVisual.groupList )
        {
            if( _Group == null )
                continue;

            foreach( var _TweenData in _Group.itemList )
            {
                if( _TweenData == null )
                    continue;

                GameObject _Target = iLeanTweenVisual.gameObject;

                // 確保目標的初始值結構已創建
                if( !_InitialValuesMap.ContainsKey( _Target ) )
                {
                    _InitialValuesMap[ _Target ] = new InitialValues( _Target );
                }

                // 設定初始值
                var _Values = _InitialValuesMap[ _Target ];
                if( _TweenData.between == LeanTweenBetween.FromTo )
                {
                    _Values.SetFromValue( _TweenData );
                }
            }
        }

        // 套用所有初始值
        foreach( var _Values in _InitialValuesMap.Values )
        {
            _Values.Apply();
        }
    }



    /// <summary>
    /// 計算 LeanTweenVisual 的總動畫時間
    /// </summary>
    /// <param name="iLeanTweenVisual">LeanTweenVisual 組件</param>
    /// <returns>總動畫時間</returns>
    private float CalculateLeanTweenVisualDuration( LeanTweenVisual iLeanTweenVisual )
    {
        if( iLeanTweenVisual == null || iLeanTweenVisual.groupList == null )
        {
            return 0f;
        }

        float _MaxDuration = 0f;

        foreach( var _Group in iLeanTweenVisual.groupList )
        {
            if( _Group?.itemList != null )
            {
                float _GroupDuration = _Group.delay;

                foreach( var _Item in _Group.itemList )
                {
                    float _ItemEndTime = _Item.delay + _Item.duration;
                    _GroupDuration = Mathf.Max( _GroupDuration, _ItemEndTime );
                }

                _MaxDuration = Mathf.Max( _MaxDuration, _GroupDuration );
            }
        }

        return _MaxDuration;
    }

    /// <summary>
    /// 項目完成回調
    /// </summary>
    private void OnItemComplete()
    {
        m_OnItemFinished?.Invoke( m_CurrentIndex );
        m_OnItemComplete?.Invoke( m_CurrentIndex );

        LogDebug( $"項目 [{m_CurrentIndex}] 完成" );

        PlayNext();
    }

    /// <summary>
    /// 序列完成回調
    /// </summary>
    private void OnSequenceComplete()
    {
        LogDebug( "序列播放完成" );

        if( m_IsLoop && ( m_LoopCount < 0 || m_CurrentLoopCount < m_LoopCount - 1 ) )
        {
            m_CurrentLoopCount++;
            LogDebug( $"開始第 {m_CurrentLoopCount + 1} 次循環" );

            m_CurrentIndex = 0;
            PlayNextItem();
        }
        else
        {
            m_CurrentState = SequenceState.Stopped;
            m_CurrentIndex = 0;
            m_CurrentLoopCount = 0;

            m_OnSequenceFinished?.Invoke();
            m_OnSequenceComplete?.Invoke();
        }
    }

    /// <summary>
    /// 除錯日誌輸出
    /// </summary>
    /// <param name="iMessage">日誌訊息</param>
    private void LogDebug( string iMessage )
    {
        if( m_ShowDebugInfo )
        {
            Debug.Log( $"[LeanTweenSequenceManager] {iMessage}" );
        }
    }
    #endregion
}
