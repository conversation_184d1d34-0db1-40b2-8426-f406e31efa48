//=====================================================================
//              CHINESE GAMER PROPRIETARY INFORMATION
//
// This software is supplied under the terms of a license agreement or
// nondisclosure agreement with CHINESE GAMER and may not 
// be copied or disclosed except in accordance with the terms of that
// agreement.
//
//                 Copyright © 2025 by CHINESE GAMER.
//                      All Rights Reserved.
//
//    -------------------------------------------------------------    
//
//=====================================================================

using DentedPixel.LTEditor;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

/// <summary>
/// 動畫序列管理器
/// <AUTHOR>
/// @telephone 2917
/// @version 1.0
/// @since 黃易群俠傳M
/// @date 2025.8.12
/// </summary>
public class UIAnimationScheduler : MonoBehaviour
{
    #region Constants
    private const float DEFAULT_DELAY_TIME = 0.1f;
    private const int MAX_SEQUENCE_COUNT = 100;
    #endregion

    #region Enums
    /// <summary>
    /// 序列項目類型
    /// </summary>
    public enum SequenceItemType
    {
        LeanTweenVisual    // LeanTweenVisual 組件
    }

    /// <summary>
    /// LeanTween 動作類型
    /// </summary>
    private enum LeanTweenAction
    {
        MOVE_X,
        MOVE_Y,
        MOVE_Z,
        MOVE_LOCAL_X,
        MOVE_LOCAL_Y,
        MOVE_LOCAL_Z,
        ROTATE_X,
        ROTATE_Y,
        ROTATE_Z,
        ROTATE_AROUND,
        ROTATE_LOCAL_X,
        ROTATE_LOCAL_Y,
        ROTATE_LOCAL_Z,
        SCALE_X,
        SCALE_Y,
        SCALE_Z,
        ALPHA
    }

    /// <summary>
    /// 序列播放狀態
    /// </summary>
    public enum SequenceState
    {
        Stopped,    // 停止
        Playing,    // 播放中
        Paused      // 暫停
    }
    #endregion

    #region Serializable Classes
    /// <summary>
    /// 序列項目資料結構
    /// </summary>
    [System.Serializable]
    public class SequenceItem
    {
        [Header( "基本設定" )]
        /// <summary>項目名稱</summary>
        public string m_Name = "Sequence Item";

        /// <summary>序列項目類型</summary>
        public SequenceItemType m_ItemType = SequenceItemType.LeanTweenVisual;

        /// <summary>是否啟用此項目</summary>
        public bool m_IsEnabled = true;

        /// <summary>此項目開始播放前的延遲秒數</summary>
        public float m_DelayTime = 0.0f;

        [Header( "LeanTweenVisual 設定" )]
        /// <summary>目標 LeanTweenVisual 組件</summary>
        public LeanTweenVisual m_LeanTweenVisual;

        /// <summary>此動畫播放完成後才會播放下個動畫</summary>
        public bool m_WaitForCompletion = true;

        #region Runtime Variables
        /// <summary>是否已初始化</summary>
        [System.NonSerialized]
        private bool m_IsInitialized = false;

        /// <summary>初始值儲存</summary>
        [System.NonSerialized]
        private InitialValues m_InitialValues;
        #endregion

        #region Public Methods
        /// <summary>
        /// 初始化序列項目
        /// </summary>
        /// <param name="iForceReinitialize">是否強制重新初始化</param>
        /// <returns>初始化是否成功</returns>
        public bool Initialize( bool iForceReinitialize = false )
        {
            // 如果已經初始化且不強制重新初始化，直接返回成功
            if( m_IsInitialized && !iForceReinitialize )
            {
                return true;
            }

            // 重置初始化狀態（每次重新初始化都會清空之前的狀態）
            m_IsInitialized = false;
            m_InitialValues = null;

            // 根據項目類型進行初始化
            switch( m_ItemType )
            {
                case SequenceItemType.LeanTweenVisual:
                    return InitializeLeanTweenVisual();

                default:
                    Debug.LogWarning( $"[UIAnimationScheduler] 未知的序列項目類型: {m_ItemType}" );
                    return false;
            }
        }

        /// <summary>
        /// 重置到初始狀態
        /// </summary>
        public void ResetToInitialState()
        {
            if( m_IsInitialized && m_InitialValues != null )
            {
                m_InitialValues.Apply();
            }
        }

        /// <summary>
        /// 檢查項目是否有效
        /// </summary>
        /// <returns>項目是否有效</returns>
        public bool IsValid()
        {
            if( !m_IsEnabled )
            {
                return false;
            }

            switch( m_ItemType )
            {
                case SequenceItemType.LeanTweenVisual:
                    return m_LeanTweenVisual != null;

                default:
                    return false;
            }
        }

        /// <summary>
        /// 取得項目的預估播放時間
        /// </summary>
        /// <returns>預估播放時間（秒）</returns>
        public float GetEstimatedDuration()
        {
            if( !IsValid() )
            {
                return 0f;
            }

            float _Duration = m_DelayTime;

            switch( m_ItemType )
            {
                case SequenceItemType.LeanTweenVisual:
                    _Duration += CalculateLeanTweenVisualDuration();
                    break;
            }

            return _Duration;
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// 初始化 LeanTweenVisual 項目
        /// </summary>
        /// <returns>初始化是否成功</returns>
        private bool InitializeLeanTweenVisual()
        {
            if( m_LeanTweenVisual == null )
            {
                Debug.LogWarning( $"[UIAnimationScheduler] SequenceItem '{m_Name}' 的 LeanTweenVisual 組件為空" );
                return false;
            }

            if( m_LeanTweenVisual.gameObject == null )
            {
                Debug.LogWarning( $"[UIAnimationScheduler] SequenceItem '{m_Name}' 的 LeanTweenVisual 目標物件為空" );
                return false;
            }

            // 建立初始值儲存
            m_InitialValues = new InitialValues( m_LeanTweenVisual.gameObject );

            // 收集並設定初始值
            CollectInitialValues();

            m_IsInitialized = true;
            return true;
        }

        /// <summary>
        /// 收集 LeanTweenVisual 的初始值
        /// </summary>
        private void CollectInitialValues()
        {
            if( m_LeanTweenVisual?.groupList == null || m_InitialValues == null )
                return;

            // 遍歷所有組並收集初始值
            foreach( var _Group in m_LeanTweenVisual.groupList )
            {
                if( _Group?.itemList == null )
                    continue;

                foreach( var _TweenData in _Group.itemList )
                {
                    if( _TweenData == null )
                        continue;

                    // 只處理 FromTo 類型的動畫，收集 From 值作為初始值
                    if( _TweenData.between == LeanTweenBetween.FromTo )
                    {
                        m_InitialValues.SetFromValue( _TweenData );
                    }
                }
            }
        }

        /// <summary>
        /// 計算 LeanTweenVisual 的總動畫時間
        /// </summary>
        /// <returns>總動畫時間</returns>
        private float CalculateLeanTweenVisualDuration()
        {
            if( m_LeanTweenVisual?.groupList == null )
            {
                return 0f;
            }

            float _MaxDuration = 0f;

            foreach( var _Group in m_LeanTweenVisual.groupList )
            {
                if( _Group?.itemList != null )
                {
                    float _GroupDuration = _Group.delay;

                    foreach( var _Item in _Group.itemList )
                    {
                        float _ItemEndTime = _Item.delay + _Item.duration;
                        _GroupDuration = Mathf.Max( _GroupDuration, _ItemEndTime );
                    }

                    _MaxDuration = Mathf.Max( _MaxDuration, _GroupDuration );
                }
            }

            return _MaxDuration;
        }
        #endregion

        #region Properties
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized => m_IsInitialized;
        #endregion
    }
    #endregion

    #region Classes
    /// <summary>
    /// 儲存物件的初始值
    /// </summary>
    private class InitialValues
    {
        private GameObject m_Target;
        private RectTransform m_RectTransform;
        private CanvasGroup m_CanvasGroup;
        private SpriteRenderer m_SpriteRenderer;
        private UnityEngine.UI.Image m_Image;

        private bool m_HasAlpha = false;
        private bool m_HasPosition = false;
        private bool m_HasScale = false;
        private bool m_HasRotation = false;
        private bool m_HasColor = false;

        private Vector3 m_Position;
        private Vector3 m_LocalPosition;
        private Vector3 m_Rotation;
        private Vector3 m_LocalRotation;
        private Vector3 m_Scale;
        private float m_Alpha;
        private Color m_Color;

        public InitialValues(GameObject iTarget)
        {
            m_Target = iTarget;
            m_RectTransform = iTarget.GetComponent<RectTransform>();
            m_CanvasGroup = iTarget.GetComponent<CanvasGroup>();
            m_SpriteRenderer = iTarget.GetComponent<SpriteRenderer>();
            m_Image = iTarget.GetComponent<UnityEngine.UI.Image>();
        }

        public void SetFromValue(DentedPixel.LTEditor.LeanTweenItem iTweenData)
        {
            var action = iTweenData.actionStr ?? "";

            if (action.Contains("MOVE"))
            {
                m_HasPosition = true;
                bool isLocal = action.Contains("LOCAL");
                Vector3 position = iTweenData.from;

                // Handle axis-specific moves
                if (action.Contains("_X") || action.Contains("_Y") || action.Contains("_Z"))
                {
                    Vector3 currentPos;
                    if (m_RectTransform != null)
                        currentPos = m_RectTransform.anchoredPosition3D;
                    else
                        currentPos = isLocal ? m_Target.transform.localPosition : m_Target.transform.position;

                    if (action.Contains("_X"))
                        position = new Vector3(iTweenData.from.x, currentPos.y, currentPos.z);
                    else if (action.Contains("_Y"))
                        position = new Vector3(currentPos.x, iTweenData.from.y, currentPos.z);
                    else if (action.Contains("_Z"))
                        position = new Vector3(currentPos.x, currentPos.y, iTweenData.from.z);
                }

                if (isLocal || m_RectTransform != null)
                    m_LocalPosition = position;
                else
                    m_Position = position;
            }
            else if (action.Contains("SCALE"))
            {
                m_HasScale = true;
                Vector3 scale = iTweenData.from;

                if (action.Contains("_X") || action.Contains("_Y") || action.Contains("_Z"))
                {
                    Vector3 currentScale = m_Target.transform.localScale;
                    if (action.Contains("_X"))
                        scale = new Vector3(iTweenData.from.x, currentScale.y, currentScale.z);
                    else if (action.Contains("_Y"))
                        scale = new Vector3(currentScale.x, iTweenData.from.y, currentScale.z);
                    else if (action.Contains("_Z"))
                        scale = new Vector3(currentScale.x, currentScale.y, iTweenData.from.z);
                }

                m_Scale = scale;
            }
            else if (action.Contains("ROTATE"))
            {
                m_HasRotation = true;
                bool isLocal = action.Contains("LOCAL");
                Vector3 rotation = iTweenData.from;

                if (action.Contains("_X") || action.Contains("_Y") || action.Contains("_Z"))
                {
                    Vector3 currentRot = isLocal ? m_Target.transform.localEulerAngles : m_Target.transform.eulerAngles;
                    if (action.Contains("_X"))
                        rotation = new Vector3(iTweenData.from.x, currentRot.y, currentRot.z);
                    else if (action.Contains("_Y"))
                        rotation = new Vector3(currentRot.x, iTweenData.from.y, currentRot.z);
                    else if (action.Contains("_Z"))
                        rotation = new Vector3(currentRot.x, currentRot.y, iTweenData.from.z);
                }

                if (isLocal)
                    m_LocalRotation = rotation;
                else
                    m_Rotation = rotation;
            }
            else if (action.Contains("ALPHA"))
            {
                m_HasAlpha = true;
                m_Alpha = iTweenData.from.x;
            }
            else if (action.Contains("COLOR"))
            {
                m_HasColor = true;
                m_Color = iTweenData.colorTo;
            }
        }

        public void Apply()
        {
            if (m_HasPosition)
            {
                if (m_RectTransform != null)
                    m_RectTransform.anchoredPosition3D = m_LocalPosition;
                else
                {
                    if (m_LocalPosition != Vector3.zero)
                        m_Target.transform.localPosition = m_LocalPosition;
                    if (m_Position != Vector3.zero)
                        m_Target.transform.position = m_Position;
                }
            }

            if (m_HasScale && m_Scale != Vector3.zero)
                m_Target.transform.localScale = m_Scale;

            if (m_HasRotation)
            {
                if (m_LocalRotation != Vector3.zero)
                    m_Target.transform.localEulerAngles = m_LocalRotation;
                else if (m_Rotation != Vector3.zero)
                    m_Target.transform.eulerAngles = m_Rotation;
            }

            if (m_HasAlpha)
            {
                if (m_CanvasGroup != null)
                    m_CanvasGroup.alpha = m_Alpha;
                else if (m_SpriteRenderer != null)
                {
                    Color color = m_SpriteRenderer.color;
                    color.a = m_Alpha;
                    m_SpriteRenderer.color = color;
                }
                else if (m_Image != null)
                {
                    Color color = m_Image.color;
                    color.a = m_Alpha;
                    m_Image.color = color;
                }
            }

            if (m_HasColor)
            {
                if (m_SpriteRenderer != null)
                    m_SpriteRenderer.color = m_Color;
                else if (m_Image != null)
                    m_Image.color = m_Color;
            }
        }
    }
    #endregion

    #region Member Variables
    [Header( "序列設定" )]
    /// <summary>序列項目清單</summary>
    public List<SequenceItem> m_SequenceItems = new List<SequenceItem>();

    [Header( "播放設定" )]
    /// <summary>是否啟動時自動播放</summary>
    public bool m_IsPlayOnStart = false;

    /// <summary>是否循環播放</summary>
    public bool m_IsLoop = false;

    /// <summary>循環次數 (-1 為無限循環)</summary>
    public int m_LoopCount = -1;

    [Header( "除錯設定" )]
    /// <summary>是否顯示除錯資訊</summary>
    public bool m_ShowDebugInfo = false;

    /// <summary>當前播放狀態</summary>
    private SequenceState m_CurrentState = SequenceState.Stopped;

    /// <summary>當前播放索引</summary>
    private int m_CurrentIndex = 0;

    /// <summary>當前循環次數</summary>
    private int m_CurrentLoopCount = 0;

    /// <summary>當前序列 ID</summary>
    private int m_CurrentSequenceId = -1;

    /// <summary>序列完成回調</summary>
    private System.Action m_OnSequenceComplete;

    /// <summary>序列項目完成回調</summary>
    private System.Action<int> m_OnItemComplete;

    /// <summary>當前播放序列是否已初始化</summary>
    private bool m_IsCurrentSequenceInitialized = false;
    #endregion

    #region Events
    [Header( "事件" )]
    /// <summary>序列開始事件</summary>
    public UnityEvent m_OnSequenceStart;

    /// <summary>序列完成事件</summary>
    public UnityEvent m_OnSequenceFinished;

    /// <summary>序列項目開始事件</summary>
    public UnityEvent<int> m_OnItemStart;

    /// <summary>序列項目完成事件</summary>
    public UnityEvent<int> m_OnItemFinished;
    #endregion

    #region Properties
    /// <summary>
    /// 當前播放狀態
    /// </summary>
    public SequenceState CurrentState => m_CurrentState;

    /// <summary>
    /// 當前播放索引
    /// </summary>
    public int CurrentIndex => m_CurrentIndex;

    /// <summary>
    /// 序列項目總數
    /// </summary>
    public int ItemCount => m_SequenceItems?.Count ?? 0;

    /// <summary>
    /// 是否正在播放
    /// </summary>
    public bool IsPlaying => m_CurrentState == SequenceState.Playing;

    /// <summary>
    /// 是否已停止
    /// </summary>
    public bool IsStopped => m_CurrentState == SequenceState.Stopped;

    /// <summary>
    /// 是否已暫停
    /// </summary>
    public bool IsPaused => m_CurrentState == SequenceState.Paused;
    #endregion

    #region Unity Lifecycle
    /// <summary>
    /// Unity Start 事件
    /// </summary>
    private void Start()
    {
        if( m_IsPlayOnStart )
        {
            PlaySequence();
        }
    }

    /// <summary>
    /// Unity OnDestroy 事件
    /// </summary>
    private void OnDestroy()
    {
        StopSequence();
    }
    #endregion

    #region Public Methods
    /// <summary>
    /// 播放序列
    /// </summary>
    /// <param name="iFromIndex">從指定索引開始播放</param>
    public void PlaySequence( int iFromIndex = 0 )
    {
        if( m_SequenceItems == null || m_SequenceItems.Count == 0 )
        {
            LogDebug( "序列項目清單為空，無法播放" );
            return;
        }

        if( m_CurrentState == SequenceState.Playing )
        {
            LogDebug( "序列已在播放中" );
            return;
        }



        m_CurrentIndex = Mathf.Clamp( iFromIndex, 0, m_SequenceItems.Count - 1 );
        m_CurrentState = SequenceState.Playing;
        m_CurrentLoopCount = 0;
        m_IsCurrentSequenceInitialized = false; // 重置初始化標記

        LogDebug( $"開始播放序列，從索引 {m_CurrentIndex} 開始" );

        m_OnSequenceStart?.Invoke();
        PlayNextItem();
    }

    /// <summary>
    /// 初始化所有序列項目
    /// </summary>
    /// <returns>初始化是否成功</returns>
    public bool InitializeAllSequenceItems()
    {
        if( m_SequenceItems == null || m_SequenceItems.Count == 0 )
        {
            LogDebug( "沒有序列項目需要初始化" );
            return true;
        }

        LogDebug( "開始初始化所有序列項目..." );

        int _SuccessCount = 0;
        int _FailCount = 0;

        for( int i = 0; i < m_SequenceItems.Count; i++ )
        {
            var _Item = m_SequenceItems[ i ];
            if( _Item == null )
            {
                LogDebug( $"序列項目 [{i}] 為空，跳過初始化" );
                _FailCount++;
                continue;
            }

            if( !_Item.m_IsEnabled )
            {
                LogDebug( $"序列項目 [{i}] '{_Item.m_Name}' 已停用，跳過初始化" );
                continue;
            }

            bool _InitResult = _Item.Initialize();
            if( _InitResult )
            {
                LogDebug( $"序列項目 [{i}] '{_Item.m_Name}' 初始化成功" );
                _SuccessCount++;
            }
            else
            {
                LogDebug( $"序列項目 [{i}] '{_Item.m_Name}' 初始化失敗" );
                _FailCount++;
            }
        }

        LogDebug( $"序列項目初始化完成：成功 {_SuccessCount} 個，失敗 {_FailCount} 個" );

        // 如果有任何項目初始化失敗，返回 false
        return _FailCount == 0;
    }

    /// <summary>
    /// 重置所有序列項目到初始狀態
    /// </summary>
    public void ResetAllSequenceItems()
    {
        if( m_SequenceItems == null || m_SequenceItems.Count == 0 )
        {
            return;
        }

        LogDebug( "重置所有序列項目到初始狀態..." );

        for( int i = 0; i < m_SequenceItems.Count; i++ )
        {
            var _Item = m_SequenceItems[ i ];
            if( _Item != null && _Item.m_IsEnabled && _Item.IsInitialized )
            {
                _Item.ResetToInitialState();
                LogDebug( $"序列項目 [{i}] '{_Item.m_Name}' 已重置到初始狀態" );
            }
        }
    }

    /// <summary>
    /// 停止序列播放
    /// </summary>
    /// <param name="iResetToInitialState">是否重置到初始狀態</param>
    public void StopSequence( bool iResetToInitialState = true )
    {
        if( m_CurrentState == SequenceState.Stopped )
        {
            return;
        }

        LogDebug( "停止序列播放" );

        // 取消當前的 LeanTween
        if( m_CurrentSequenceId != -1 )
        {
            LeanTween.cancel( m_CurrentSequenceId );
            m_CurrentSequenceId = -1;
        }

        // 停止當前播放的 LeanTweenVisual
        if( m_CurrentIndex >= 0 && m_CurrentIndex < m_SequenceItems.Count )
        {
            var _CurrentItem = m_SequenceItems[ m_CurrentIndex ];
            if( _CurrentItem.m_LeanTweenVisual != null )
            {
                LeanTween.cancel( _CurrentItem.m_LeanTweenVisual.gameObject );
            }
        }

        // 停止所有可能正在播放的動畫
        StopAllActiveAnimations();

        // 重置到初始狀態（如果需要）
        if( iResetToInitialState )
        {
            ResetAllSequenceItems();
        }

        m_CurrentState = SequenceState.Stopped;
        m_CurrentIndex = 0;
        m_CurrentLoopCount = 0;
        m_IsCurrentSequenceInitialized = false; // 重置初始化標記
    }

    /// <summary>
    /// 停止所有正在播放的動畫
    /// </summary>
    private void StopAllActiveAnimations()
    {
        if( m_SequenceItems == null )
            return;

        for( int i = 0; i < m_SequenceItems.Count; i++ )
        {
            var _Item = m_SequenceItems[ i ];
            if( _Item != null && _Item.m_LeanTweenVisual != null && _Item.m_LeanTweenVisual.gameObject != null )
            {
                LeanTween.cancel( _Item.m_LeanTweenVisual.gameObject );
            }
        }
    }

    /// <summary>
    /// 暫停序列播放
    /// </summary>
    public void PauseSequence()
    {
        if( m_CurrentState == SequenceState.Playing )
        {
            LogDebug( "暫停序列播放" );
            m_CurrentState = SequenceState.Paused;

            // 暫停當前的 LeanTween
            if( m_CurrentSequenceId != -1 )
            {
                LeanTween.pause( m_CurrentSequenceId );
            }
        }
    }

    /// <summary>
    /// 恢復序列播放
    /// </summary>
    public void ResumeSequence()
    {
        if( m_CurrentState == SequenceState.Paused )
        {
            LogDebug( "恢復序列播放" );
            m_CurrentState = SequenceState.Playing;

            // 恢復當前的 LeanTween
            if( m_CurrentSequenceId != -1 )
            {
                LeanTween.resume( m_CurrentSequenceId );
            }
        }
    }

    /// <summary>
    /// 跳到指定索引並播放
    /// </summary>
    /// <param name="iIndex">目標索引</param>
    public void JumpToIndex( int iIndex )
    {
        if( iIndex < 0 || iIndex >= m_SequenceItems.Count )
        {
            LogDebug( $"索引 {iIndex} 超出範圍" );
            return;
        }

        StopSequence();
        PlaySequence( iIndex );
    }

    /// <summary>
    /// 播放下一個項目
    /// </summary>
    public void PlayNext()
    {
        if( m_CurrentState != SequenceState.Playing )
        {
            return;
        }

        m_CurrentIndex++;
        if( m_CurrentIndex >= m_SequenceItems.Count )
        {
            OnSequenceComplete();
        }
        else
        {
            PlayNextItem();
        }
    }

    /// <summary>
    /// 播放上一個項目
    /// </summary>
    public void PlayPrevious()
    {
        if( m_CurrentIndex > 0 )
        {
            JumpToIndex( m_CurrentIndex - 1 );
        }
    }

    /// <summary>
    /// 強制重新初始化所有序列項目（每次播放都會執行）
    /// </summary>
    /// <returns>初始化是否成功</returns>
    public bool ForceReinitializeAllSequenceItems()
    {
        if( m_SequenceItems == null || m_SequenceItems.Count == 0 )
        {
            LogDebug( "沒有序列項目需要初始化" );
            return true;
        }

        LogDebug( "開始初始化所有序列項目（每次播放都會重新初始化）..." );

        int _SuccessCount = 0;
        int _FailCount = 0;

        for( int i = 0; i < m_SequenceItems.Count; i++ )
        {
            var _Item = m_SequenceItems[ i ];
            if( _Item == null )
            {
                LogDebug( $"序列項目 [{i}] 為空，跳過初始化" );
                _FailCount++;
                continue;
            }

            if( !_Item.m_IsEnabled )
            {
                LogDebug( $"序列項目 [{i}] '{_Item.m_Name}' 已停用，跳過初始化" );
                continue;
            }

            bool _InitResult = _Item.Initialize( true ); // 強制重新初始化
            if( _InitResult )
            {
                LogDebug( $"序列項目 [{i}] '{_Item.m_Name}' 初始化成功" );
                _SuccessCount++;
            }
            else
            {
                LogDebug( $"序列項目 [{i}] '{_Item.m_Name}' 初始化失敗" );
                _FailCount++;
            }
        }

        LogDebug( $"序列項目初始化完成：成功 {_SuccessCount} 個，失敗 {_FailCount} 個" );
        return _FailCount == 0;
    }

    /// <summary>
    /// 取得序列的總預估播放時間
    /// </summary>
    /// <returns>總預估播放時間（秒）</returns>
    public float GetTotalEstimatedDuration()
    {
        if( m_SequenceItems == null || m_SequenceItems.Count == 0 )
        {
            return 0f;
        }

        float _TotalDuration = 0f;

        for( int i = 0; i < m_SequenceItems.Count; i++ )
        {
            var _Item = m_SequenceItems[ i ];
            if( _Item != null && _Item.m_IsEnabled )
            {
                _TotalDuration += _Item.GetEstimatedDuration();
            }
        }

        return _TotalDuration;
    }

    /// <summary>
    /// 檢查所有序列項目是否有效
    /// </summary>
    /// <returns>所有項目是否有效</returns>
    public bool ValidateAllSequenceItems()
    {
        if( m_SequenceItems == null || m_SequenceItems.Count == 0 )
        {
            LogDebug( "沒有序列項目需要驗證" );
            return true;
        }

        bool _AllValid = true;
        int _ValidCount = 0;
        int _InvalidCount = 0;

        for( int i = 0; i < m_SequenceItems.Count; i++ )
        {
            var _Item = m_SequenceItems[ i ];
            if( _Item == null )
            {
                LogDebug( $"序列項目 [{i}] 為空" );
                _InvalidCount++;
                _AllValid = false;
                continue;
            }

            if( !_Item.m_IsEnabled )
            {
                continue; // 停用的項目不需要驗證
            }

            if( _Item.IsValid() )
            {
                _ValidCount++;
            }
            else
            {
                LogDebug( $"序列項目 [{i}] '{_Item.m_Name}' 無效" );
                _InvalidCount++;
                _AllValid = false;
            }
        }

        LogDebug( $"序列項目驗證完成：有效 {_ValidCount} 個，無效 {_InvalidCount} 個" );
        return _AllValid;
    }
    #endregion

    #region Private Methods
    /// <summary>
    /// 播放下一個項目
    /// </summary>
    private void PlayNextItem()
    {
        if( m_CurrentState != SequenceState.Playing || m_CurrentIndex >= m_SequenceItems.Count )
        {
            return;
        }

        // 如果是播放第一個項目且尚未初始化，初始化所有 SequenceItem
        if( m_CurrentIndex == 0 && !m_IsCurrentSequenceInitialized )
        {
            LogDebug( "開始播放第一個項目，初始化所有 SequenceItem..." );
            if( !ForceReinitializeAllSequenceItems() )
            {
                LogDebug( "序列項目初始化失敗，停止播放" );
                StopSequence( false ); // 不重置狀態，因為初始化失敗
                return;
            }
            m_IsCurrentSequenceInitialized = true; // 標記已初始化
        }

        var _CurrentItem = m_SequenceItems[ m_CurrentIndex ];

        if( !_CurrentItem.m_IsEnabled )
        {
            LogDebug( $"跳過已停用的項目 [{m_CurrentIndex}]: {_CurrentItem.m_Name}" );
            PlayNext();
            return;
        }

        LogDebug( $"準備播放項目 [{m_CurrentIndex}]: {_CurrentItem.m_Name} (類型: {_CurrentItem.m_ItemType})" );

        // 處理延遲時間
        if( _CurrentItem.m_DelayTime > 0 )
        {
            LogDebug( $"等待 {_CurrentItem.m_DelayTime} 秒後開始播放項目: {_CurrentItem.m_Name}" );
            m_CurrentSequenceId = LeanTween.delayedCall( _CurrentItem.m_DelayTime, () => {
                ExecuteItemPlay( _CurrentItem );
            } ).id;
            return;
        }

        ExecuteItemPlay( _CurrentItem );
    }

    /// <summary>
    /// 執行項目播放
    /// </summary>
    /// <param name="iItem">序列項目</param>
    private void ExecuteItemPlay( SequenceItem iItem )
    {
        m_OnItemStart?.Invoke( m_CurrentIndex );
        
        LogDebug( $"開始播放項目: {iItem.m_Name}" );

        switch( iItem.m_ItemType )
        {
            case SequenceItemType.LeanTweenVisual:
                PlayLeanTweenVisualItem( iItem );
                break;

            default:
                LogDebug( $"未知的項目類型: {iItem.m_ItemType}" );
                PlayNext();
                break;
        }
    }

    /// <summary>
    /// 播放 LeanTweenVisual 項目
    /// </summary>
    /// <param name="iItem">序列項目</param>
    private void PlayLeanTweenVisualItem( SequenceItem iItem )
    {
        if( iItem.m_LeanTweenVisual == null )
        {
            LogDebug( $"LeanTweenVisual 組件為空，跳過項目: {iItem.m_Name}" );
            PlayNext();
            return;
        }

        // 確保項目已初始化（由於每次播放都會重新初始化，這裡應該已經初始化完成）
        if( !iItem.IsInitialized )
        {
            LogDebug( $"警告：項目 '{iItem.m_Name}' 在播放時尚未初始化，這不應該發生" );
            PlayNext();
            return;
        }

        // 重置到初始狀態
        iItem.ResetToInitialState();

        // 啟動 LeanTweenVisual 動畫
        iItem.m_LeanTweenVisual.buildAllTweens( false );

        if( iItem.m_WaitForCompletion )
        {
            // 使用項目自己的時間計算方法
            float _TotalDuration = iItem.GetEstimatedDuration() - iItem.m_DelayTime; // 減去延遲時間，因為延遲已經在外層處理

            if( _TotalDuration > 0 )
            {
                // 等待動畫完成
                m_CurrentSequenceId = LeanTween.delayedCall( _TotalDuration, () => {
                    OnItemComplete();
                } ).id;
            }
            else
            {
                // 如果無法計算時間，立即播放下一個
                PlayNext();
            }
        }
        else
        {
            // 不等待完成，立即播放下一個
            PlayNext();
        }
    }



    /// <summary>
    /// 項目完成回調
    /// </summary>
    private void OnItemComplete()
    {
        m_OnItemFinished?.Invoke( m_CurrentIndex );
        m_OnItemComplete?.Invoke( m_CurrentIndex );

        LogDebug( $"項目 [{m_CurrentIndex}] 完成" );

        PlayNext();
    }

    /// <summary>
    /// 序列完成回調
    /// </summary>
    private void OnSequenceComplete()
    {
        LogDebug( "序列播放完成" );

        if( m_IsLoop && ( m_LoopCount < 0 || m_CurrentLoopCount < m_LoopCount - 1 ) )
        {
            m_CurrentLoopCount++;
            LogDebug( $"開始第 {m_CurrentLoopCount + 1} 次循環" );

            // 循環播放時，重置索引到第一個項目並重置初始化標記
            // 初始化會在 PlayNextItem 中自動處理（因為 m_CurrentIndex == 0 且 m_IsCurrentSequenceInitialized 會被重置）
            m_CurrentIndex = 0;
            m_IsCurrentSequenceInitialized = false; // 重置初始化標記，讓下次循環重新初始化
            PlayNextItem();
        }
        else
        {
            // 序列完成，重置所有項目到初始狀態
            ResetAllSequenceItems();

            m_CurrentState = SequenceState.Stopped;
            m_CurrentIndex = 0;
            m_CurrentLoopCount = 0;
            m_IsCurrentSequenceInitialized = false; // 重置初始化標記

            m_OnSequenceFinished?.Invoke();
            m_OnSequenceComplete?.Invoke();
        }
    }

    /// <summary>
    /// 除錯日誌輸出
    /// </summary>
    /// <param name="iMessage">日誌訊息</param>
    private void LogDebug( string iMessage )
    {
        if( m_ShowDebugInfo )
        {
            Debug.Log( $"[LeanTweenSequenceManager] {iMessage}" );
        }
    }
    #endregion
}
