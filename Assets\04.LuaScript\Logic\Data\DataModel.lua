---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

require("Data/TextData")
require("Data/IllegalNameData")
require("Data/RandomNameData")
require("Data/ItemData")
require("Data/MenuData")
require("Data/PetData")
require("Data/PetAttributeData")
require("Data/TitleData")
require("Data/AppearanceData")

require("Data/TickData")
require("Data/SkillData")
require("Data/WugongData")
require("Data/WugongGapeData")

require("Data/SceneAttributeData")
require("Data/NPCData")
require("Data/TabData")
require("Data/GuildDomainData")
require("Data/GuildGiftData")
require("Data/GuildLvData")
require("Data/CaptionTheaterData")

require("Data/PartyData")
require("Data/LoveData")
require("Data/LoveActionData")
require("Data/LiveSkillData")
require("Data/PetTalentData")
require("Data/PetStoreData")
require("Data/PetSkillData")
require("Data/PetPowerData")
require("Data/ActivityTag")
require("Data/ActivityContent")
require("Data/MallData")
require("Data/MailData")
require("Data/MallMainTagData")
require("Data/MallSubTagData")
require("Data/GamblingData")
require("Data/GemData")
require("Data/GameMaze")
require("Data/FuDaiData")
require("Data/FormulaData")
require("Data/CurrencyData")
require("Data/CoverData")
require("Data/BuffData")
require("Data/BuffGroup")
require("Data/BannerData")
require("Data/AutoDBSwitchData")
require("Data/StatusNameData")
require("Data/CashRewardData")
require("Data/CashFlowData")
require("Data/CashBonusData")
require("Data/SpecialSkillBtnData")
require("Data/BossGPSData")
require("Data/NotificationData")
require("Data/SiegeMapData")
require("Data/ActBoxData")
require("Data/ActBoxContentData")
require("Data/SellData")
require("Data/SiegeTurretData")
require("Data/DyeingColorData")
require("Data/ChangeweaponData")
require("Data/ActivitySkillData")
require("Data/ComposeData")
require("Data/ComposeTabsData")
require("Data/DisconnectData")
require("Data/CourseRewardsData")
require("Data/DecomposeData")
require("Data/SkillActData")
require("Data/CommonQuery")
require("Data/EnhanceColorData")
require("Data/EnhanceData")
require("Data/EnhanceFXData")
require("Data/EnhanceProbData")
require("Data/ExchangeConditionData")
require("Data/ExchangeItemData")
require("Data/ExchangeShopData")
require("Data/ExchangeCardData")
require("Data/StageData")
require("Data/WardrobeData")
require("Data/SuitData")
require("Data/FeatureData")
require("Data/MapLinkData")
require("Data/BattleTextData")
require("Data/QuestListData")
require("Data/TeachingStepsData")
require("Data/UITeachData")
require("Data/FacialFeatureData")
require("Data/WugongPreviewData")
require("Data/TakemedicineData")
require("Data/LivingSkillinfoData")

require("Data/HeadIconData")
require("Data/HeadFrameData")

require("Data/WorldMapData")
require("Data/MapTabsData")
require("Data/MapNapTabsData")
require("Data/EventCollisionClassData")
require("Data/MapNpcClassData")

require("Data/TimeMachineData")
require("Data/TMRoomEffectData")

require("Data/LevelData")
require("Data/EnergyMineData")
require("Data/CampaignData")
require("Data/CampaignPassData")

require("Data/NPCTreasureData")
require("Data/ExplorePoint_Prize")
require("Data/QuestPrize")

require("Data/FriendRank")

require("Data/CombineItem")
require("Data/PetFuseData")
require("Data/RandomSkill")

require("Data/QuickMessageData")
require("Data/EmojiData")
require("Data/PetMissionData")

--region 黑市商人串檔資料 Added by 凌傑RM#120024 0121
require("Data/MysticShopData")
require("Data/MysticItemData")
--endregion

require("Data/ModifyArmorData")
require("Data/ModifyBrainData")
require("Data/ModifyCustomizeData")


require("Data/AchievementID")
require("Data/AchievementTitle")


require("Data/CollectTitle")
require("Data/CollectData")

--region 內功系統
require("Data/InnerBookData")
require("Data/InnerMeridianData")
require("Data/InnerMeridianSkillData")
--endregion

---author 鼎翰
---telephone #2917
---version 1.0
---since [黃易群俠傳M] 0.91
---date 2024.1.18

---一般串檔的 Model
---@class DataModel
DataModel = {}
local this = DataModel

---儲存所有一般串檔 Cotoutine 的 table
---@type table
this.m_DataCoroutineTable = {}

---儲存所有一般串檔的 table
---@type table
this.m_DataTable = {
    AchievementID,
    AchievementTitle,
    ActBoxContentData,
    ActBoxData,
    ActivityContent,
    ActivitySkillData,
    ActivityTag,
    AppearanceData,
    AutoDBSwitchData,
    BattleTextData,
    BannerData,
    BossGPSData,
    BuffData,
    BuffGroup,
    CampaignData,
    CampaignPassData,
    CaptionTheaterData,
    CashBonusData,
    CashFlowData,
    CashRewardData,
    ChangeweaponData,
    CollectData,
    CollectTitle,
    CombineItem,
    CommonQuery,
    ComposeData,
    ComposeTabsData,
    CourseRewardsData,
    CoverData,
    CurrencyData,
    DecomposeData,
    DisconnectData,
    DyeingColorData,
    EmojiData,
    EnhanceColorData,
    EnhanceData,
    EnhanceFXData,
    EnhanceProbData,
    EnergyMineData,
    EventCollisionClassData,
    ExchangeCardData,
    ExchangeConditionData,
    ExchangeItemData,
    ExchangeShopData,
    ExplorePoint_Prize,
    FacialFeatureData,
    FeatureData,
    FormulaData,
    FriendRank,
    FuDaiData,
    GamblingData,
    GameMaze,
    GemData,
    GuildDomainData,
    GuildGiftData,
    GuildLvData,
    HeadFrameData,
    HeadIconData,
    IllegalNameData,
    InnerBookData,
    InnerMeridianData,
    InnerMeridianSkillData,
    ItemData,
    LevelData,
    LiveSkillData,
    LivingSkillinfoData,
    LoveActionData,
    LoveData,
    MailData,
    MallData,
    MallMainTagData,
    MallSubTagData,
    MapLinkData,
    MapNapTabsData,
    MapNpcClassData,
    MapTabsData,
    MenuData,
    ModifyArmorData,
    ModifyBrainData,
    ModifyCustomizeData,
    MysticItemData,
    MysticShopData,
    NotificationData,
    NPCData,
    NPCTreasureData,
    PartyData,
    PetAttributeData,
    PetData,
    PetFuseData,
    PetMissionData,
    PetPowerData,
    PetSkillData,
    PetStoreData,
    PetTalentData,
    QuickMessageData,
    QuestListData,
    QuestPrize,
    RandomNameData,
    RandomSkill,
    SceneAttributeData,
    SellData,
    SiegeMapData,
    SiegeTurretData,
    SkillData,
    SkillActData,
    SpecialSkillBtnData,
    StageData,
    StatusNameData,
    SuitData,
    TabData,
    TakemedicineData,
    TeachingStepsData,
    TextData,
    TickData,
    TimeMachineData,
    TitleData,
    TMRoomEffectData,
    UITeachData,
    WardrobeData,
    WorldMapData,
    WugongData,
    WugongGapeData,
    WugongPreviewData,
}

---一般串檔 因為切換語言需要更新的table
DataModel.m_ChangeLanguageRnewTable = {
	TextData,
}

--取得一般串檔筆數
function DataModel.GetAllData()
    return this.m_DataTable
end

---取得一般串檔總數
function DataModel.GetDataCount()
    return table.Count(this.m_DataTable)
end

---是否包含再依般一般串檔內
function DataModel.Contains(iData)
    return table.Contains(this.m_DataTable, iData)
end