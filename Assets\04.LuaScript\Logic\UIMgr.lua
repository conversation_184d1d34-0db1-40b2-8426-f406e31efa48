---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2021 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

require( "Logic/SystemSettingData/UISetting" )
require( "Logic/SystemSettingData/UIAnimationSetting" )
require( "Logic/SystemSettingData/TabTextSetting" )
require( "Logic/SystemSettingData/CastBarSetting" )
require( "UI/UIBackGround/UIBackGround_Controller" )

---UI管理器
---author KK
---version 1.0
---since [ProjectBase] 0.1
---date 2021.10.12
UIMgr = {}
local this = UIMgr

---關閉 Loading 的延遲時間
Loading_Close_Wait_Time = 1.5

---等待關閉中的 LoadingUI
local _WaitCloseLoadingUI = nil

---等待 LoadingUI 關閉後要開啟的 UI
local _EventUIWaitOpenWhenLoadingUIClose = nil

---是否是登入的第一次開啟主介面
local m_IsTheFirstTimeOpenMainAfterLogin = false

---UI 依照永標顯示控制開關
this.m_IsUseUIStaticFlagCtrl = false

---是否開啟邊界內縮
this.m_IsUseBoundaryCtrl = false

---哪個 UI 開啟時要關閉 Loading
this.m_CloseLoadingWhenUIOpen = nil

---已開啟的劇情動畫 UI
this.m_OpenedPlotAnimationUI = nil

---目前 Loading 開啟類型
this.m_NowLoadingOpenType = ELoadingOpenType.Defult

---關閉是否要直接回主畫面
this.m_IsCloseBackToMain = false

---UI 內縮距離
this.m_UIIndentationDistance ={
	Horizontal = 0,
	Vertical = 0,
}

---UI 元件寬高縮放設定
this.m_MatchWidthOrHeight ={
	---是否裝置檔已經設定
	IsDeviceAlreadySet = false,
	---設定的縮放數值 最小0 最大1
	Value = 1,
}

---UI 寬高基準
---@class EMatchWidthOrHeight
local EMatchWidthOrHeight = {
	---寬為準
	Width = 0,
	---以高為準
	Height = 1,
}

---UI 在Canvas 的 Order in Layer 範圍 HalfPage 沒有 因為會依附在 最後一個全版去計算
local _EUIOrderRangeTable = {}
_EUIOrderRangeTable[EUIOrderLayers.FullPage] = 1000
_EUIOrderRangeTable[EUIOrderLayers.Peak] = 3000
_EUIOrderRangeTable[EUIOrderLayers.Debug] = 5000

--- UI 最頂層的 Number
local _TheTopOrderNumber = 10000

---現在 Order 的 UI
local _EUIOrderTable = {}
_EUIOrderTable[EUIOrderLayers.HalfPage_Left] = {}
_EUIOrderTable[EUIOrderLayers.HalfPage_Right] = {}
_EUIOrderTable[EUIOrderLayers.HalfPage_Center] = {}
_EUIOrderTable[EUIOrderLayers.FullPage] = {}
_EUIOrderTable[EUIOrderLayers.Peak] = {}
_EUIOrderTable[EUIOrderLayers.Debug] = {}
---現在 Order 的半版UI 因半版介面分成3個 UIOrderTable 但 sortingOrder 要合併計算
local _HalfUIOrderTable = {}
---UI 在各自的 OrderLayerTable 內的最大數量
local _EUIMaxInLayersTableCount = 
{
	[EUIOrderLayers.HalfPage_Left] = 
	{
		[EDeviceLevel.Low] = 3,
		[EDeviceLevel.Medium] = 4,
		[EDeviceLevel.High] = 5,
	},
	[EUIOrderLayers.HalfPage_Right] = 
	{
		[EDeviceLevel.Low] = 3,
		[EDeviceLevel.Medium] = 4,
		[EDeviceLevel.High] = 5,
	},
	[EUIOrderLayers.HalfPage_Center] = 
	{
		[EDeviceLevel.Low] = 3,
		[EDeviceLevel.Medium] = 4,
		[EDeviceLevel.High] = 5,
	},
	[EUIOrderLayers.FullPage] = 
	{
		[EDeviceLevel.Low] = 2,
		[EDeviceLevel.Medium] = 4,
		[EDeviceLevel.High] = 5,
	},
	[EUIOrderLayers.Peak] = 
	{
		[EDeviceLevel.Low] = 3,
		[EDeviceLevel.Medium] = 4,
		[EDeviceLevel.High] = 5,
	},
	[EUIOrderLayers.Debug] =
	{
		[EDeviceLevel.Low] = 10,
		[EDeviceLevel.Medium] = 10,
		[EDeviceLevel.High] = 10,
	},
}

---不能被銷毀物件的 UI
---@type table
local _Table_ConnotDestroy = {}
---不能被銷毀物件的 UI (左邊半版)
_Table_ConnotDestroy[EUIOrderLayers.HalfPage_Left] = {
	RoleAttribute_Controller,
}
---不能被銷毀物件的 UI (右邊半版)
_Table_ConnotDestroy[EUIOrderLayers.HalfPage_Right] = {
	MiniMap_Controller,
	HotKey_Controller,
	Bag_Controller
}
_Table_ConnotDestroy[EUIOrderLayers.HalfPage_Center] = {
	Tracing_Controller,
	AVG_Controller,
	Menu_Controller,
	Death_Controller
}

---不能被銷毀物件的 UI (全版)
_Table_ConnotDestroy[EUIOrderLayers.FullPage] = {
	UIBlackBase_Controller,
	Main_Controller,
}

---不能被銷毀物件的 UI (頂層)
_Table_ConnotDestroy[EUIOrderLayers.Peak] = {
	Loading_Controller,
	UIOrderTop_Controller,
	NumberPanel_Controller,
	HintMgr_Controller
}

---不能被銷毀物件的 UI 登出時
local _Table_ConnotDestroy_LoggingOut = {
	Loading_Controller,
	UIBlackBase_Controller,
	Debug_Controller,
	UIOrderTop_Controller,
}
---不能被銷毀物件的 UI 結束Lua時
local _Table_ConnotDestroy_QuitLua = {
	Loading_Controller,
}

---Loading 的 Table
local _Table_Loading = {
	Loading_Controller,
	CaptionTheater_Controller,
}

---關閉 PeakTable 的例外
local _ExpceptClosePeakTableWhenUIOpen = {
	UIOrderTop_Controller,
	CommonQuery_Type1_Controller,
	CommonQuery_Type2_Controller,
	CommonQuery_Type3_Controller,
	CommonQuery_Type4_Controller,
	CommonQuery_Type5_Controller,
	CommonQuery_Type6_Controller,
	CommonQuery_Type7_Controller,
	CommonQuery_BlackBackGround_Controller,
	Teach_Controller,
	HintMgr_Controller,
}

---變數
---UI開啟順序的堆疊 堆疊結構 {[1] = UIController, [2] = iIsForceUseArgs, [3] = iParams}
---@type Stack
local _Stack_UIFullPage = Stack.New()

---在 UIMgr 內的 UI 資料
---@type table
local _Table_UI = {}

---在場上的 UI 資料
---@type table
local _Table_UIInHierarchy = {}

---UI 物件的名稱後綴
---@type string
local _String_ViewBack = "_View"

---不需要 HUD 且 停止走路的 UI
---@type table
local _Table_NeedCloseHUDAndWalkWhenUIOpen = {}

---在關閉 UI 時 UI 還未完成開啟 要等待關閉的 UI
---@type table
local _Table_WaitUIOpendToClose = {}

---短時間大量開啟UI時需要排隊依序開啟

---排隊中需要依序開啟的排隊UI
local _DeletaGateFunctiobTable = {}

---目前正在開啟中的排隊UI
local _Cur_Opened_WaitingUI = nil

---CommonRewardMgr 是否已經演出完畢並允許演出快速提示
local _AllowFastClick_CommonRewardMgr = true

---排隊中UI 是否已經演出完畢並允許演出快速提示
local _AllowFastClick_DelayUI = true

---被定義 需要排隊演出的 排隊UI 順序也是優先顯示順序
local _Table_DelayUI=
{
	---教學介面
	Teach_Controller,
	--- 任務完成介面
	CommonReward_MissionComplete_Controller,
	--- 腳色升級介面
	CommonReward_LevelUpgrade_Controller,
	---任務完成 自選獎勵介面
	SelectList_Controller,
	--- 裝備替換提醒介面
	PopItemChange_Controller,
	---對話介面
	EventTalk_Controller,

}

---@type table
local _ScreenView = {}

--- UI 由 Event 開啟 的暫存 Queue 每個 Item 的結構 {[1] = UIController, [2] = iParams}
---@type Queue
local _Queue_CurrentSaveUIOpenByEvent = Queue:New()

---等待開啟的 Loading 結構 {[1] = UIController, [2] = iParams}
local _WaitOpenLoading ={
	[1] = nil,
	[2] = {}
}

--- UI 移動關閉類型時 關閉的位置
---@type Vector3
local _UIStorePlace = Vector3.New(0, 0)

---因為切換語言登出實需要重新初始化的UI
this.m_LanguageDependUITable =
{
	MiniMap_Controller,
	Main_Controller,
	Tracing_Controller,
	AVG_Controller,
	Menu_Controller,
	NumberPanel_Controller,
	Bag_Controller,
	RoleAttribute_Controller,

	--Loading_Controller,
	EventTalk_Controller,
	TimeLine_Controller,
	Setting_Controller,

}

---不需要調整高度的 UI
local m_Table_UIDoNotAdjustHeigth ={
	Main_Controller,
	Teach_Controller,
}

--region local function

---該UI是否屬於半版介面
---@param iUI UIController
local function IsHalfPage(iUI)
	if iUI.m_UIOrderLayerKey == EUIOrderLayers.HalfPage_Left or iUI.m_UIOrderLayerKey == EUIOrderLayers.HalfPage_Right or iUI.m_UIOrderLayerKey == EUIOrderLayers.HalfPage_Center then
		return true
	else
		return false
	end
end

--region UI Order 設定相關

---驗證 UI Order 設定的有效性
---@param iUI table UI Controller
---@return boolean
local function ValidateUIOrder(iUI)
	if iUI == nil then
		return false
	end
	if string.IsNullOrEmpty(iUI.m_UIOrderLayerKey) then
		D.LogWarning(iUI.m_Name..":沒有設定 Order 層級")
		return false
	end
	if _EUIOrderTable[iUI.m_UIOrderLayerKey] == nil then
		D.LogError(iUI.m_Name..":找不到所設定的 Order 層級")
		return false
	end
	return true
end

---計算 UI 基礎排序值
---@param iUI table UI Controller
---@return number 基礎排序值
local function CalculateBaseOrder(iUI)
	if IsHalfPage(iUI) then
		if table.Count(_EUIOrderTable[EUIOrderLayers.FullPage]) > 0 then
			return _EUIOrderTable[EUIOrderLayers.FullPage][table.Count(_EUIOrderTable[EUIOrderLayers.FullPage])].m_Canvas.sortingOrder
		end
		return _EUIOrderRangeTable[EUIOrderLayers.FullPage]
	end
	return _EUIOrderRangeTable[iUI.m_UIOrderLayerKey]
end

---計算 UI Order 值
---@param iUI table UI Controller
---@return number Order 值
local function CalculateUIOrder(iUI)
	local _UIOrderTable = _EUIOrderTable[iUI.m_UIOrderLayerKey]
	local _BaseOrderCount = CalculateBaseOrder(iUI)
	
	-- 特殊處理 UIOrderTop_Controller
	if _UIOrderTable == _EUIOrderTable[EUIOrderLayers.Peak] and iUI == UIOrderTop_Controller then
		return _TheTopOrderNumber
	end
	
	-- 使用統一的計算邏輯
	local _Count = IsHalfPage(iUI) and table.Count(_HalfUIOrderTable) or table.Count(_UIOrderTable)
	return _BaseOrderCount + (_Count * UI_LAYER_ORDER_VALUE_ADD)
end

---處理 Peak UI 特殊情況
---@param iUI table UI Controller
---@param iUIOrderTable table UI Order Table
---@return table|nil Teach Controller
local function HandlePeakUITeach(iUI, iUIOrderTable)
	if iUI.m_UIOrderLayerKey == EUIOrderLayers.Peak 
	and iUI ~= Teach_Controller 
	and table.Contains(iUIOrderTable, Teach_Controller) then
		local _CurrentKey = table.GetKey(iUIOrderTable, Teach_Controller)
		return table.remove(iUIOrderTable, _CurrentKey)
	end
	return nil
end

---更新半版 UI Order
---@param iUI table UI Controller
---@param iUIOrderTable table UI Order Table
---@param iBaseOrderCount number 基礎排序值
local function UpdateHalfPageOrder(iUI, iUIOrderTable, iBaseOrderCount)
	for _Key, _Value in pairs(_HalfUIOrderTable) do
		local _Index = table.GetKey(_EUIOrderTable[_Value.m_UIOrderLayerKey], _Value)
		local _NowOrder = iBaseOrderCount + _Key * UI_LAYER_ORDER_VALUE_ADD
		local _CurrentUI = _EUIOrderTable[_Value.m_UIOrderLayerKey][_Index]
		if _CurrentUI.m_Canvas.sortingOrder ~= _NowOrder then
			_CurrentUI.m_Canvas.overrideSorting = true
			_CurrentUI.m_Canvas.sortingOrder = _NowOrder
		end
	end
end

---更新一般 UI Order
---@param iUIOrderTable table UI Order Table
---@param iBaseOrderCount number 基礎排序值
local function UpdateNormalOrder(iUIOrderTable, iBaseOrderCount)
	local _Index = 1
	for _Key, _Value in pairs(iUIOrderTable) do
		if table.IsNullOrEmpty(iUIOrderTable[_Index]) then
			iUIOrderTable[_Index] = _Value
			table.remove(iUIOrderTable, _Key)
		end
		local _NowOrder = iBaseOrderCount + _Index * UI_LAYER_ORDER_VALUE_ADD
		if iUIOrderTable[_Index].m_Canvas ~= nil 
		and iUIOrderTable[_Index].m_Canvas.sortingOrder ~= _NowOrder then
			iUIOrderTable[_Index].m_Canvas.overrideSorting = true
			iUIOrderTable[_Index].m_Canvas.sortingOrder = _NowOrder
		end
		_Index = _Index + 1
	end
end

---更新 UI Background Order
---@param iUI table UI Controller
local function UpdateUIBackground(iUI)
	if iUI.m_UIOrderLayerKey == EUIOrderLayers.FullPage then
		this.m_Canvas_BG.overrideSorting = true
		this.m_Canvas_BGMask.overrideSorting = true
		this.m_Canvas_BG.sortingOrder = iUI.m_Canvas.sortingOrder - 2
		this.m_Canvas_BGMask.sortingOrder = iUI.m_Canvas.sortingOrder - 3
	end
end

---計算各 UI 的 Order 值
---@param iUI table UI Controller
local function SetUIOrder(iUI)
	-- 驗證輸入
	if not ValidateUIOrder(iUI) then return end
	
	-- 初始化變數
	local _UIOrderTable = _EUIOrderTable[iUI.m_UIOrderLayerKey]
	local _BaseOrderCount = CalculateBaseOrder(iUI)
	local _UIOrderTopUI, _UILoading = nil, nil
	local _UITeach = HandlePeakUITeach(iUI, _UIOrderTable)

	-- 處理現有 UI
	if table.Contains(_UIOrderTable, iUI) then
		local _CurrentKey = table.GetKey(_UIOrderTable, iUI)
		if _CurrentKey ~= nil then
			iUI = table.remove(_UIOrderTable, _CurrentKey)
			if IsHalfPage(iUI) then
				table.remove(_HalfUIOrderTable, table.GetKey(_HalfUIOrderTable, iUI))
			end
		end

		-- 處理特殊 UI
		if _UIOrderTable == _EUIOrderTable[EUIOrderLayers.Peak] then
			if iUI ~= UIOrderTop_Controller and table.Contains(_UIOrderTable, UIOrderTop_Controller) then
				_UIOrderTopUI = table.remove(_UIOrderTable, table.GetKey(_UIOrderTable, UIOrderTop_Controller))
			end
			if iUI ~= Loading_Controller and table.Contains(_UIOrderTable, Loading_Controller) then
				_UILoading = table.remove(_UIOrderTable, table.GetKey(_UIOrderTable, Loading_Controller))
			end
		end

		-- 更新 Order
		if IsHalfPage(iUI) then
			UpdateHalfPageOrder(iUI, _UIOrderTable, _BaseOrderCount)
		else
			UpdateNormalOrder(_UIOrderTable, _BaseOrderCount)
		end
	end

	-- 更新目標 UI
	table.insert(_UIOrderTable, table.Count(_UIOrderTable) + 1, iUI)
	if IsHalfPage(iUI) then
		table.insert(_HalfUIOrderTable, table.Count(_HalfUIOrderTable) + 1, iUI)
	end
	iUI.m_Canvas.overrideSorting = true

	-- 設定 Order
	if _UIOrderTable == _EUIOrderTable[EUIOrderLayers.Peak] and iUI == UIOrderTop_Controller then
		iUI.m_Canvas.sortingOrder = _TheTopOrderNumber
	else
		iUI.m_Canvas.sortingOrder = CalculateUIOrder(iUI)
	end

	-- 更新背景
	UpdateUIBackground(iUI)

	-- 重新插入特殊 UI
	if _UIOrderTopUI then
		table.insert(_UIOrderTable, table.Count(_UIOrderTable) + 1, _UIOrderTopUI)
	end
	if _UITeach then
		table.insert(_UIOrderTable, table.Count(_UIOrderTable) + 1, _UITeach)
		_UITeach.m_Canvas.sortingOrder = _BaseOrderCount + (table.Count(_UIOrderTable) * UI_LAYER_ORDER_VALUE_ADD)
	end
	if _UILoading then
		table.insert(_UIOrderTable, table.Count(_UIOrderTable) + 1, _UILoading)
	end

	---因為中央訊息也要搬移到Peak階層 詢問視窗開啟時 在開中央訊息會在誠 共詢問視窗的黑幕order 亂跑 因此需要再做一次 共用詢問視窗的黑幕 跟共用詢問視窗的 order 檢查
	--
	if UIMgr.IsVisible(CommonQuery_BlackBackGround_Controller) and 
		UIMgr.IsVisible(CommonQueryMgr.m_CurrentUseUI)  and 
		CommonQueryMgr.m_CurrentUseUI.m_Canvas.sortingOrder < CommonQuery_BlackBackGround_Controller.m_Canvas.sortingOrder and 
		_UIOrderTable == _EUIOrderTable[EUIOrderLayers.Peak] then
		
		CommonQuery_BlackBackGround_Controller.SetChangeOrderBool(true)
	end
	
end
--endregion

---獲取指定 UI 的當前 Order 值 [SetUIOrder 在 UI.Open 後才會計算，需要在設定 UIOrder 前作處裡的可以取此 function]
---@param iUI table UI Controller
---@return number|nil 返回 UI 的 Order 值，如果無效則返回 nil
function UIMgr.GetUICurrentOrder(iUI)
	if not ValidateUIOrder(iUI) then return nil end
	
	--- 因為 UI 還未設定進 指定的 _UIOrderTable 所以預先取得的時候要多加一個 UI_LAYER_ORDER_VALUE_ADD
	return CalculateUIOrder(iUI) + UI_LAYER_ORDER_VALUE_ADD 
end

---取得螢幕方向設定
local function GetScreenOrientation()
	--編輯器一開始不會設定 所以 Screen.orientation 永遠都會抓到 1
	if ProjectMgr.IsEditor() then
		if Screen.height > Screen.width then
			--直式螢幕
			return ScreenOrientation.Portrait:ToInt()
		else
			--橫式螢幕 Landscape 已被 LandscapeLeft 取代
			return ScreenOrientation.LandscapeLeft:ToInt()
		end
	else
		return Screen.orientation:ToInt()
	end
end

---依照螢幕比例調整 Pivot
local function GetPivot()
	-- 先以橫式螢幕為準
	local _Pivot =  Vector2.New(0.5,1)

	if ProjectMgr.IsPC() then
		--if EScreenratio[EResolutionStyle.Default].x < EScreenratio[EResolutionStyle.Default].y then
			-- 改為直式螢幕為準
			--_Pivot = Vector2.New(0.5,0)
		--end
	elseif ProjectMgr.IsAndroid() or ProjectMgr.IsiOS() then
		local _ScreenOrientation = GetScreenOrientation()
		if _ScreenOrientation == ScreenOrientation.Portrait:ToInt() or _ScreenOrientation == ScreenOrientation.PortraitUpsideDown:ToInt() then
			-- 改為直式螢幕為準
			_Pivot = Vector2.New(0.5,0)
		elseif _ScreenOrientation == ScreenOrientation.LandscapeLeft:ToInt() or _ScreenOrientation == ScreenOrientation.LandscapeRight:ToInt() then
			-- 改為橫式螢幕為準
			_Pivot =  Vector2.New(0.5,1)
		end
	end

	return _Pivot
end

---設定 UI 內縮距離
local function InitUIIndentationDistance()
	this.m_UIIndentationDistance = ClientSaveMgr.GetDataValue(EClientSaveDataType.Device,"m_UIIndentationDistance")
end

---設定 UI 寬高縮放設定
local function InitUIMatchWidthOrHeight()
	this.m_MatchWidthOrHeight = ClientSaveMgr.GetDataValue(EClientSaveDataType.Device,"m_MatchWidthOrHeight")
end

---檢查要不要開教學
---@param iUI table 要檢查的 UI
local function OpenCheckTeach(iUI)
	if iUI == Teach_Controller then
		return
	end
	if iUI.m_Index == nil then
		return
	end

	---目前教學的步驟
	local _CurrentTeachStepUI = TeachMgr.GetCurrentTeachStepUI()

	--如果正在教學中 而且不是最後一個步驟[那就會是在教學步驟中開啟的 UI]
	if not TeachMgr.IsLastTeaching() and TeachMgr.IsTeaching() and iUI ~= _CurrentTeachStepUI then
		--送下一步
		TeachMgr.NextStep()
	else
		--檢查 有沒有要開啟教學 (先關閉)
		--[[local _UITeachData = UITeachData.GetUITeachDataByIdx(iUI.m_Index)
		if not table.IsNullOrEmpty(_UITeachData) then
			---此 UI 的教學資料比數
			local _TeachDataCount = table.Count(_UITeachData.m_UITeachStep)
			--檢查 UI 是否有可以開啟的 TeachID
			if _TeachDataCount > 0 then
				for i = 1, _TeachDataCount do
					if _UITeachData.m_UITeachStep[i] > 0 then
						local _IsCanTeach = TeachMgr.CheckCanTeachAndStartTeach(_UITeachData.m_UITeachStep[i], true)
						--可以教學就不往後檢查了
						if _IsCanTeach then
							return
						end
					end
				end
			end
		end]]
	end

end

---檢查各 UI 內元件依照各自永標顯示
local function CheckUIComponentsVisible(iUI)
	--檢查 UI 能不能開
	local _UISettingData = UISetting.GetUISettingData(iUI)
	--要開的時候再檢查
	if _UISettingData ~= nil and _UISettingData.m_ComponentsVisibleData ~= nil then
		for key, value in pairs(_UISettingData.m_ComponentsVisibleData) do
			this.SetUIComponentVisible(iUI, key,value)
		end
	end
end

---是否是 劇情動畫的 UI
local function IsPlotAnimationUI(iUI)
	return iUI == TimeLine_Controller or iUI == CGPlayer_Controller
end

---檢查是不是登入後第一次開啟主介面(過場成功)完成
local function CheckFirstTimeOpenMainAfterLogin()
	--登入後 第一次開啟主介面(過場成功)完成要做甚麼
	if Main_Controller:IsVisible()  and Login_Model.m_IsLogin == true and not m_IsTheFirstTimeOpenMainAfterLogin then
		Game.ToDoWhenFirstTimeOpenMainAfterLogin()
		m_IsTheFirstTimeOpenMainAfterLogin = true
	end
end

---取得 Loading 當前對應的 UI
local function GetCurrentLoadingUI()
	---Loading 當前對應的 UI
	local _CurrentUI = nil
	if this.m_NowLoadingOpenType == ELoadingOpenType.Defult then
		_CurrentUI = Loading_Controller
	elseif this.m_NowLoadingOpenType == ELoadingOpenType.CaptionTheater then
		_CurrentUI = CaptionTheater_Controller
	elseif this.m_NowLoadingOpenType == ELoadingOpenType.Effect then
		_CurrentUI = nil
	else
		_CurrentUI = nil
	end

	return _CurrentUI
end

---開啟下一個事件的 UI
local function OpenNextEventUI()
	--檢查第一筆事件的 UI
	local IsFirstEventDoing, _CurrentUITable = this.IsFirstEventDoing()
	--如果不在轉場中 或是 在轉場中但是要關閉轉場的 UI 是事件的第一個
	if not SceneMgr.m_IsLoadingScene
	or (SceneMgr.m_IsLoadingScene and this.m_CloseLoadingWhenUIOpen ~= nil) then
		--檢查 是否可已啟用 UI
		if not IsFirstEventDoing then
			--沒有在過場中先 且 Loading 還沒關 (原因為等待關閉秒數間收到協定)
			if _WaitCloseLoadingUI ~= nil then
				_EventUIWaitOpenWhenLoadingUIClose = _CurrentUITable
				_WaitCloseLoadingUI = nil
				return
			end
			--檢查通過 開啟事件的 UI
			_CurrentUITable[1].m_IsDoingEvent = true
			--正在開的事件 UI 是否是劇情動畫 UI
			if IsPlotAnimationUI(_CurrentUITable[1]) then
				--劇情動畫 UI 是否正在等待被下個事件 UI 關閉
				if this.m_OpenedPlotAnimationUI ~= nil and  this.m_OpenedPlotAnimationUI.m_IsWaitNextEventClose then
					--正在開的事件 UI 是否與正在等待被下個事件 UI 關閉 的 UI 相同
					if _CurrentUITable[1] == this.m_OpenedPlotAnimationUI then
						this.m_OpenedPlotAnimationUI.Close()
					else
						UIMgr.CloseToPreviousPage(this.m_OpenedPlotAnimationUI)
					end
				end
			else
				--劇情動畫 UI 是否正在等待被下個事件 UI 關閉
				if this.m_OpenedPlotAnimationUI ~= nil and  this.m_OpenedPlotAnimationUI.m_IsWaitNextEventClose then
					UIMgr.CloseToPreviousPage(this.m_OpenedPlotAnimationUI)
				end
			end

			this.Open(_CurrentUITable[1], unpack(_CurrentUITable[2], 1, table.maxn(_CurrentUITable[2])))
		end
	end
end

---切換半版顯示
---@param iUI UIController
---@param iIsSwitch boolean 當前切換的顯示
function UIMgr.SwitchViewVisible(iUI, iIsSwitch)
	---預計關閉的版面
	local _CloseUILayerKey = iUI.m_UIOrderLayerKey

	---當介面重疊時，是否為左右邊半版底層介面
	if iIsSwitch == false and (iUI.m_UIOrderLayerKey == EUIOrderLayers.HalfPage_Left or iUI.m_UIOrderLayerKey == EUIOrderLayers.HalfPage_Right) then
		local _Layertable = {}

		for key, value in pairs(UIMgr.GetUIOrderTable(_CloseUILayerKey)) do
			if iUI ~= value and not table.Contains(Main_Model.EUIINMain, value) then
				table.insert(_Layertable, value)
			end
		end

		if table.Count(_Layertable) > 0 then
			UIMgr.CloseByTable(_Layertable, Main_Model.EUIINMain)
		end
	end

	Main_Controller.IsShowPage(iIsSwitch, _CloseUILayerKey)
end

---關閉 Prefab
local function CloseUI(iUI, iParams)
	--沒此 UI 的物件 跳掉
	if iUI.m_ViewRef.gameObject == nil then
		return false
	end
	--此 UI 沒顯示 跳掉
	if this.GetUIStage(iUI) ~= EUIStage.Closing and not this.IsVisible(iUI) then
		return true
	end

	--劇情動畫 UI 關閉時要處理
	if iUI == this.m_OpenedPlotAnimationUI and this.m_OpenedPlotAnimationUI.m_NextEventID > 0 and not this.m_OpenedPlotAnimationUI.m_IsWaitNextEventClose then
		this.m_OpenedPlotAnimationUI.m_IsWaitNextEventClose = true
		--把第一筆丟出來
		_Queue_CurrentSaveUIOpenByEvent:Dequeue()
		--把正在關閉的事件 UI 標記結束
		iUI.m_IsDoingEvent = false
		if this.m_OpenedPlotAnimationUI.SetCoverBlackActive ~= nil then
			this.m_OpenedPlotAnimationUI.SetCoverBlackActive(true)
		end
		local _IsHaveEventUINeedOpen, _CurrentUITable = UIMgr.IsHaveEventUINeedOpen()
		if not _IsHaveEventUINeedOpen then
			---如果有等待開啟的 Loading
			if _WaitOpenLoading[1] ~= nil then
				this.Open(_WaitOpenLoading[1], unpack(_WaitOpenLoading[2], 1,table.maxn(_WaitOpenLoading[2])))
				--復原變數
				_WaitOpenLoading[1] = nil
				_WaitOpenLoading[2] = {}
			else
				if this.m_OpenedPlotAnimationUI == TimeLine_Controller then
					TimeLine_Model.SendTimeLineEnd()
				else
					SendProtocol_006._009()
				end
			end
		else
			OpenNextEventUI()
		end

		if this.m_OpenedPlotAnimationUI ~= nil then
			D.Log(GString.GetTextWithColor("劇情動畫 UI["..this.m_OpenedPlotAnimationUI.m_Name.."] 等待下一 UI 開啟事件關閉中，等待之事件ID["..this.m_OpenedPlotAnimationUI.m_NextEventID.."]", Color.Gold))
		end
		return
	end

	--此 UI 有 Close function
	if iUI.Close ~= nil then
		iUI.Close(iParams)
	end

	--此 UI 有需要控制其他 UI 的開關
	if table.Count(iUI.m_Table_VisibleControlOtherUI) > 0 then
		this.CloseByTable(iUI.m_Table_VisibleControlOtherUI)
	end

	--此 UI 是否有 關閉效果
	if iUI.OnCloseEffect ~= nil then
		iUI.OnCloseEffect()
	else
		this.SetUIPrefab(iUI, false)
	end

	UIMgr.SwitchViewVisible(iUI, true)
end

---設定 HUD 和停止走路
local function SetHUDAndCtrlWalk(iIsActive)
	if not iIsActive then
		--停止走路
		MoveMgr.PlayerRestartVectorMove()
		MoveMgr.PlayerStopFirst(true)

		if AutoBattleMgr.m_AutoBattleState == AutoBattleMgr.EAutoBattleState.Play then
			-- 暫停自動戰鬥
			AutoBattleMgr.SetAutoBattle(AutoBattleMgr.EAutoBattleState.Pause)
		end
		BattleMgr.SetAutoNormalBattle(false)
	else
		if AutoBattleMgr.m_AutoBattleState == AutoBattleMgr.EAutoBattleState.Pause then
			-- 如果是暫停，那就再把他打開
			AutoBattleMgr.SetAutoBattle(AutoBattleMgr.EAutoBattleState.Play)
		end
	end
	--關 HUD
	CameraMgr.SetHUDCameraActive(iIsActive)
	--關 SelectMgr
	SelectMgr.SetActive(iIsActive)
end

local function IsCanSetHUDAndCtrlWalk(iUI, iIsOpen)
	if not table.Contains(_Table_NeedCloseHUDAndWalkWhenUIOpen, iUI) then
		return false
	end

	if _Stack_UIFullPage:Count() > 0 then
		local _PreviousStackUI = _Stack_UIFullPage:Peek()
		if _PreviousStackUI[1] ~= iUI and table.Contains(_Table_NeedCloseHUDAndWalkWhenUIOpen, _PreviousStackUI[1]) then
			return false
		end
	end

	local _IsNeedCtrl = false
	for key, value in pairs(_Table_NeedCloseHUDAndWalkWhenUIOpen) do
		if value == iUI then
			_IsNeedCtrl = true
			break
		end
	end

	if not iIsOpen then
		for key, value in pairs(_Table_NeedCloseHUDAndWalkWhenUIOpen) do
			if this.IsVisible(value) then
				_IsNeedCtrl = false
				break
			end
		end
	end

	return _IsNeedCtrl
end

--- 清除 UI
---@param iUI table 各 UIController
local function DestroyUI(iUI)
	---是否可以Destroy UI
	local IsCanDestroyUI = iUI:Destroy()
	--可以 Destroy 就執行
	if IsCanDestroyUI then
		---全版的話先關 BG
		if iUI.m_UIOrderLayerKey == EUIOrderLayers.FullPage then
			if iUI.m_Name == UIBackGround_Controller.m_NowUseUIBackGroundUIName then
				UIBackGround_Controller.SetUIBackgroundVisiable(false)
			end
		end
		--從紀錄刪除
		table.removeByKey(_Table_UI, iUI.m_UIView)
		table.removeByKey(_Table_UIInHierarchy, iUI.m_UIView)
		table.removeByKey(_Table_WaitUIOpendToClose, iUI.m_UIView)
		local _Index = table.GetKey(_EUIOrderTable[iUI.m_UIOrderLayerKey], iUI)
		if _Index ~= nil then
			table.remove(_EUIOrderTable[iUI.m_UIOrderLayerKey], _Index)

		end
		--- 半版介面有多紀錄在 _HalfUIOrderTable 同步刪除 2025.01.07 Modify by 蛋糕
		if IsHalfPage(iUI) then
			local _HalfPageIndex = table.GetKey(_HalfUIOrderTable, iUI)
			table.remove(_HalfUIOrderTable, _HalfPageIndex)
		end
		_Stack_UIFullPage:Remove(iUI)

		D.Log("UI Destroy 並Unload: ".. iUI.m_UIView)
		ResourceMgr.Unload(iUI.m_UIView)
	end
	return IsCanDestroyUI
end

---檢查各 UI 階層數量
---@param iUI table 各 UIController
local function CheckUICount(iUI)
	---目前要檢查的最大數量
	local _CheckMaxCount = _EUIMaxInLayersTableCount[iUI.m_UIOrderLayerKey][SettingMgr.m_DeviceLevel] + table.Count(_Table_ConnotDestroy[iUI.m_UIOrderLayerKey])
	---目前要檢查的 Table
	local _CheckTable = _EUIOrderTable[iUI.m_UIOrderLayerKey]
	---目前不能刪除的
	local _CheckTable_ConnotDestroy = _Table_ConnotDestroy[iUI.m_UIOrderLayerKey]
	for key, value in pairs(_CheckTable) do
		---檢查數量有沒有超過
		if table.Count(_CheckTable) > _CheckMaxCount then
			--沒有包含在不能刪的 Table 裡面
			if value ~= iUI and not table.Contains(_CheckTable_ConnotDestroy, value) then
				DestroyUI(value)
			end
		else
			break
		end
	end
end

---檢查是否需要關閉相對應的 Loading UI
local function CheckCloseLoading(iUI)
	if iUI.m_UIOrderLayerKey  == EUIOrderLayers.Debug or IsHalfPage(iUI) then
		return
	end
	--需要檢查的 UI
	local _CurrentLoadingUI = GetCurrentLoadingUI()
	--登入 且 正在換場中
	local _IsLoadingScene = Login_Model.m_IsLogin and SceneMgr.m_IsLoadingScene
	--未登入 且 串檔已初始化完成
	local _IsAllDataInitialized = not Login_Model.m_IsLogin and DataMgr.IsAllDataInitialized()
	---因為語言切換觸發的重新登入
	local _IsReLoginByLanguage = Login_Model.m_ReLogin_Language


	--關閉 Loading 前置條件符合
	local _IsPreconditionsforLodingUIClosingAreMet = _IsLoadingScene or _IsAllDataInitialized or _IsReLoginByLanguage
	--是否有需要關閉的 Loading UI
	local _IsNeedColseLoadingUI = (_IsLoadingScene and (this.m_NowLoadingOpenType == ELoadingOpenType.None or this.m_NowLoadingOpenType == ELoadingOpenType.Effect) and _CurrentLoadingUI == nil)
								or ( this.m_NowLoadingOpenType ~= ELoadingOpenType.None and _CurrentLoadingUI ~= nil and this.IsVisible(_CurrentLoadingUI))
	--是否需要判斷 關閉 Loading
	if _IsPreconditionsforLodingUIClosingAreMet and _IsNeedColseLoadingUI then
		if this.m_CloseLoadingWhenUIOpen ~= nil and iUI == this.m_CloseLoadingWhenUIOpen then
			coroutine.start(function()
				--復原過場變數
				SceneMgr.m_IsLoadingScene = false
				this.m_CloseLoadingWhenUIOpen = nil
				this.m_NowLoadingOpenType = -1
				if _CurrentLoadingUI ~= nil then
					---是否收到事件需要開啟 UI
					local _IsHaveEventUINeedOpen, _CurrentUITable = this.IsHaveEventUINeedOpen()
					if _IsHaveEventUINeedOpen and not this.IsFirstEventDoing() then
						this.m_CloseLoadingWhenUIOpen = _CurrentUITable[1]
						OpenNextEventUI()
						return
					end
					_WaitCloseLoadingUI = _CurrentLoadingUI
					coroutine.wait(Loading_Close_Wait_Time)
					if not SceneMgr.m_IsLoadingScene then
						this.CloseToPreviousPage(_CurrentLoadingUI)
						if Login_Model.m_ReLogin_Language == true then
							Login_Model.m_ReLogin_Language = false
						end
						_WaitCloseLoadingUI = nil
					end
				end
				coroutine.stop()
			end)
		end
	end
end

---在過場後是否有事件開啟的 UI 需要被開啟
local function IsNeedCloseCurrentOpenAndOpenNextEventUI(iUI, iIsForceUseArgs, iArgs )
	if IsHalfPage(iUI) or iUI.m_UIOrderLayerKey == EUIOrderLayers.Debug then
		return false
	end
	---是否收到事件需要開啟 UI
	local _IsHaveEventUINeedOpen, _CurrentUITable = this.IsHaveEventUINeedOpen()
	--檢查第一筆事件的 UI
	local _IsFirstEventDoing = this.IsFirstEventDoing()
	---需要關閉現在正再開啟的 UI
	local _IsNeedCloseCurrentOpenAndOpenNextEventUI = false
	--是否正在 Loading
	local _IsLoandingNow = SceneMgr.m_IsLoadingScene and iUI == this.m_CloseLoadingWhenUIOpen
	--是否正在開啟主介面
	local _IsOpeningMain = iUI == Main_Controller
	_IsNeedCloseCurrentOpenAndOpenNextEventUI = (_IsHaveEventUINeedOpen and not _IsFirstEventDoing and _IsLoandingNow) or (_IsHaveEventUINeedOpen and not _IsFirstEventDoing  and _IsOpeningMain)

	if _IsNeedCloseCurrentOpenAndOpenNextEventUI  then
		--如果是在 Loading 中
		if _IsLoandingNow then
			--需要檢查的 UI
			local _CurrentUI = GetCurrentLoadingUI()
			--是否需要置換判斷 關閉 Loading 的 UI
			local _IsNeedChangeColseLoadingUI = (this.m_NowLoadingOpenType == ELoadingOpenType.None and _CurrentUI == nil) or (iUI ~= _CurrentUI and this.IsVisible(_CurrentUI))
			if _IsNeedChangeColseLoadingUI then
				--把事件 UI 加入關閉 Loading
				this.m_CloseLoadingWhenUIOpen = _CurrentUITable[1]
			end
		end

		--正在開啟的UI 是否要堆疊
		local _IsNeedPreviousStack = iUI.m_UIOrderLayerKey == EUIOrderLayers.FullPage and iUI.m_IsAutomaticStackingFullPage ~= nil and iUI.m_IsAutomaticStackingFullPage
		if _IsNeedPreviousStack then
			local _Push = true
			if _Stack_UIFullPage:Count() > 0 then
				--關閉的介面
				local _PreUI = _Stack_UIFullPage:Peek()
				--如果目前已經開啟的 UI 跟正要開啟的 UI 一樣 那就不用再 Push
				if _PreUI[1] == iUI then
					_Push = false
				end
				if _Push then
					--如果 要關閉的介面 有控制其他介面關閉的話 Call 關閉
					if _PreUI[1].m_Table_VisibleControlOtherUI ~= nil and table.Count(_PreUI[1].m_Table_VisibleControlOtherUI) > 0 then
						this.CloseByTable(_PreUI[1].m_Table_VisibleControlOtherUI)
					end

					---關掉已經開啟的 UI
					_PreUI[1].m_UIStage = EUIStage.Closing
					--已經開啟的 UI 有 Close function
					if _PreUI[1].Close ~= nil then
						_PreUI[1].Close()
					end

					--已經開啟的 UI 是否有 關閉效果
					if _PreUI[1].OnCloseEffect ~= nil then
						_PreUI[1].OnCloseEffect()
					else
						this.SetUIPrefab(_PreUI[1], false)
					end

					this.RemoveUI(_PreUI[1])
				end
			end
			if _Push then
				--將正在開啟的 UI 加入堆疊中
				_Stack_UIFullPage:Push({ iUI, iIsForceUseArgs, iArgs })
			end
		end

		--正要開啟的 UI 關閉
		iUI.m_UIStage = EUIStage.Closed

		--開啟下個 事件的 UI
		OpenNextEventUI()
		return true
	end

	return false
end

---檢查控制其他 UI 的開關的 UI 是否都初始化完成
---@param iUI UIControllerBase 
local function CheckAllControlSubUIIsReady(iUI)
	---檢查是否都已經完成初始化
	local _AllUIReady = false
	while not _AllUIReady do
		_AllUIReady = true
		for _, _NeedCtrlUI in pairs(iUI.m_Table_VisibleControlOtherUI) do
			if not UIMgr.IsInitialized(_NeedCtrlUI) then
				_AllUIReady = false
				break
			end
		end
		--沒有完成，等 0.05 秒
		if not _AllUIReady then
			coroutine.wait(0.05)
		end
	end
	this.OpenByTable(iUI.m_Table_VisibleControlOtherUI)
end

---開啟 Prefab
local function OpenUI(iUI, iParams)
	if iUI.m_ViewRef == nil then
		D.LogError("UI：["..iUI.m_UIController.."] m_ViewRef is nil")
	end
	--找不到物件 不能開
	if iUI.m_ViewRef.gameObject == nil then
		return false
	end

	--顯示中 且不能重複呼叫 Open
	if this.IsVisible(iUI) and not iUI.m_IsUICanCallOpenAgain then
		return true
	end

	--是否完成Open
	local _IsDoneOpenFunction = false

	--此 UI 是否有 Open function
	if iUI.Open ~= nil then
		_IsDoneOpenFunction = iUI.Open(iParams)
		iUI:TranslationText()
	else
		_IsDoneOpenFunction = true
	end

	--無法順利執行 XXX_Controller.Open()
	if _IsDoneOpenFunction == nil or not _IsDoneOpenFunction then
		iUI.m_UIStage = EUIStage.Closing
		this.SetUIPrefab(iUI, false)
		--如果沒有開成功的介面是 要關閉 Loading 的介面
		IsNeedCloseCurrentOpenAndOpenNextEventUI(iUI, false)
		return _IsDoneOpenFunction
	end

	if not this.IsVisible(iUI) then
		this.SetUIPrefab(iUI, true)
		if iUI.OnOpenEffect ~= nil then
			iUI.OnOpenEffect()
		end
	end

	--此 UI 有需要控制其他 UI 的開關 需檢查 因為在 Preload 時有先預載 iUI.m_Table_VisibleControlOtherUI 的 UI
	if table.Count(iUI.m_Table_VisibleControlOtherUI) > 0 then
		local _NeedWait = false
		for _, _NeedCtrlUI in pairs(iUI.m_Table_VisibleControlOtherUI) do
			if not UIMgr.IsInitialized(_NeedCtrlUI) then
				_NeedWait = true
				break
			end
		end
		if _NeedWait then
			-- 啟動協程等待所有 UI 初始化完成
			coroutine.start(function()
				CheckAllControlSubUIIsReady(iUI)
			end)
		else
			this.OpenByTable(iUI.m_Table_VisibleControlOtherUI)
		end
	end

	return _IsDoneOpenFunction
end

---開啟 UI 成功後，要做甚麼
---@param iUI table 各 UIController
local function ToDoAfterOpenUISucceed(iUI)
	if UIMgr.GetUIStage(iUI) == EUIStage.DoingOpenEffect or UIMgr.GetUIStage(iUI) == EUIStage.Opening then
		--改階段 開啟完成
		iUI.m_UIStage = EUIStage.Opened
	else
		D.Log("開啟階段錯誤，UI ："..iUI.m_Name)
		return
	end
	--檢查各 UI 階層數量
	CheckUICount(iUI)
	--更新 UI 邊界
	this.SetUIBound(iUI)
	--檢查要不要開教學
	OpenCheckTeach(iUI)
	--重設主介面所需
	if iUI.m_UIOrderLayerKey == EUIOrderLayers.FullPage and this.IsVisible(Main_Controller) then
		Main_Controller.ResetWhenOtherUIOpen()
	end

	--檢查是否要關閉 HUD
	if IsCanSetHUDAndCtrlWalk(iUI, true) then
		SetHUDAndCtrlWalk(false)
	end

	---如果是需要關閉 Loading 的介面
	if iUI == this.m_CloseLoadingWhenUIOpen then
		--登入 且 正在換場中
		local _IsLoadingScene = Login_Model.m_IsLogin and SceneMgr.m_IsLoadingScene
		--未登入 且 串檔已初始化完成
		local _IsAllDataInitialized = not Login_Model.m_IsLogin and DataMgr.IsAllDataInitialized()
		---因為語言切換觸發的重新登入
		local _IsReLoginByLanguage = Login_Model.m_ReLogin_Language

		--關閉 Loading 前置條件符合
		local _IsPreconditionsforLodingUIClosingAreMet = _IsLoadingScene or _IsAllDataInitialized or _IsReLoginByLanguage

		if _IsPreconditionsforLodingUIClosingAreMet then
			--檢查是否需要關閉相對應的 Loading UI
			CheckCloseLoading(iUI)
		else
			if iUI == Main_Controller and this.IsHaveEventUINeedOpen() then
				OpenNextEventUI()
			end
		end
	end

	---是否有正在開啟的 LoadingUI
	local _CurrentLoadingUI = GetCurrentLoadingUI()

	if iUI == _CurrentLoadingUI then
		---是否需要關閉動畫 UI
		local _IsNeedClosePlotAnimationUI = this.m_OpenedPlotAnimationUI ~= nil and this.m_OpenedPlotAnimationUI.m_IsWaitNextEventClose == true
		---是否動畫 UI 有正在顯示
		local _IsPlotAnimationUIVisible = this.m_OpenedPlotAnimationUI ~= nil and this.m_OpenedPlotAnimationUI:IsInitialized() and this.m_OpenedPlotAnimationUI:IsVisible()
		if _IsNeedClosePlotAnimationUI and _IsPlotAnimationUIVisible then
			UIMgr.CloseToPreviousPage(this.m_OpenedPlotAnimationUI)
		end
	end

	if not table.GetKey(Main_Model.EUIINMain, iUI) then
		UIMgr.SwitchViewVisible(iUI, false)
	end

	--UI 完成開啟後要做甚麼事 [各介面自己決定要不要實作]
	if iUI.ToDoAfterOpenUISucceed ~= nil then
		iUI.ToDoAfterOpenUISucceed()
	end

	---每次登入 第一次開主介面(過場完成)的時候要檢查
	if iUI == Main_Controller and not m_IsTheFirstTimeOpenMainAfterLogin then
		coroutine.start(function()
			while this.m_OpenedPlotAnimationUI ~= nil or this.IsHaveEventUINeedOpen() or GetCurrentLoadingUI() ~= nil do
				coroutine.wait(0.1)
			end
			CheckFirstTimeOpenMainAfterLogin()
			coroutine.stop()
		end)
	end
end

---初始化 及 註冊 UI 開關時 顯示效果
local function InitUIVisibleEffect(iUI)
	if UIAnimationSetting.m_UIAnimationData[iUI.m_Index] ~= nil then
		UIVisibleEffect.InitVisibleEffect(iUI)
		--開啟效果
		iUI.OnOpenEffect = function ()
			
			--改階段 正在開啟中
			iUI.m_UIStage = EUIStage.DoingOpenEffect
			UIVisibleEffect.DoVisibleEffect(iUI, nil, true)
			if iUI.m_UIOrderLayerKey == EUIOrderLayers.FullPage then
				UIBackGround_Controller.ReAssignEffect(UIAnimationSetting.m_UIAnimationData[iUI.m_Index])
				UIBackGround_Controller.BackGroundDoEffect(true)
			end
			
		end
		--關閉效果
		iUI.OnCloseEffect = function ()
			--改階段 正在關閉中
			iUI.m_UIStage = EUIStage.DoingCloseEffect
			UIVisibleEffect.DoVisibleEffect(iUI, nil, false)
			if iUI.m_UIOrderLayerKey == EUIOrderLayers.FullPage then
				UIBackGround_Controller.ReAssignEffect(UIAnimationSetting.m_UIAnimationData[iUI.m_Index])
				UIBackGround_Controller.BackGroundDoEffect(false)
			end
		end
	end

	local _EffectList = iUI.m_ViewRef.m_Dic_UIAnimation
	local _Keys
	if _EffectList then
		_Keys = _EffectList:GetKeys()
		for i = 0, _EffectList:Count() - 1 do
			local _k = _Keys[i]
			local _UIAnimation = _EffectList:Get(_k)
			if _UIAnimation and UIVisibleEffect.m_UIVisibleEffectData[_UIAnimation.m_AnimationType] then
				UIVisibleEffect.InitVisibleEffect(_UIAnimation, _UIAnimation.m_AnimationType)
				_UIAnimation:SetAnimationFunction(
				function()
					UIVisibleEffect.InitVisibleEffect(_UIAnimation, _UIAnimation.m_AnimationType)
				end,
				function()
					UIVisibleEffect.DoVisibleEffect(_UIAnimation, _UIAnimation.m_AnimationType, true)
				end)
			end
			if _UIAnimation and UIVisibleEffect.m_UIVisibleEffectData[_UIAnimation.m_AnimationTypeClose] then
				_UIAnimation:SetAnimationFunctionClose(
				function()
					UIVisibleEffect.InitVisibleEffect(_UIAnimation, _UIAnimation.m_AnimationTypeClose)
				end,
				function()
					UIVisibleEffect.DoVisibleEffect(_UIAnimation, _UIAnimation.m_AnimationTypeClose, false)
				end)
			end
		end
	end
end

---取得目前 UI 要放的父物件
local function GetUIParent(iUI)
	if iUI.m_UIOrderLayerKey ~= nil then
		if iUI.m_UIOrderLayerKey == EUIOrderLayers.FullPage then
			return this.m_Transform_FullPage
		elseif IsHalfPage(iUI) then
			return this.m_Transform_HalfPage
		elseif iUI.m_UIOrderLayerKey == EUIOrderLayers.Peak then
			return this.m_Transform_Peak
		elseif iUI.m_UIOrderLayerKey == EUIOrderLayers.Debug then
			return this.m_Transform_Debug
		else
			return this.transform
		end
	else
		return this.transform
	end
end

--- 檢查堆疊第一個是否為全版介面且 OnCloseEffect ~= nil
--- @return boolean, UIController 需要等待關閉後開啟則回傳 true，否則 false, 需要等待關閉後開啟的 UI
function UIMgr.IsNeedWaitFullPageCloseEffect()
	if _Stack_UIFullPage:Count() > 0 then
		local _PreUI = _Stack_UIFullPage:Peek()
		local _CurrentUI = _PreUI[1]
		if _CurrentUI and _CurrentUI.m_UIOrderLayerKey == EUIOrderLayers.FullPage and _CurrentUI.OnCloseEffect ~= nil then
			return true, _CurrentUI
		end
	end
	return false, nil
end

function UIMgr.UIBackGroundClose(iUI)
	--如果 要關閉的介面 有控制其他介面關閉的話 Call 關閉
	if iUI.m_Table_VisibleControlOtherUI ~= nil and table.Count(iUI.m_Table_VisibleControlOtherUI) > 0 then
		this.CloseByTable(iUI.m_Table_VisibleControlOtherUI)
	end

	---關掉已經開啟的 UI
	iUI.m_UIStage = EUIStage.Closing
	--已經開啟的 UI 有 Close function
	if iUI.Close ~= nil then
		iUI.Close(iParams)
	end

	--已經開啟的 UI 是否有 關閉效果
	if iUI.OnCloseEffect ~= nil then
		iUI.OnCloseEffect()
	else
		this.SetUIPrefab(iUI, false)
	end

	this.RemoveUI(iUI)
end

---UI 堆疊檢查
local function CheckUIStack(iUI, iIsForceUseArgs, iParams)
	--開啟時的檢查
	if this.GetUIStage(iUI) == EUIStage.Opening then
		--如果 此 UI 事需要堆疊的 UI
		local _IsNeedPreviousStack = iUI.m_IsAutomaticStackingFullPage ~= nil and iUI.m_IsAutomaticStackingFullPage
		if _IsNeedPreviousStack or iIsForceUseArgs then
			--檢查堆疊
			local _Push = true
			--如果前面有其他 UI
			if _Stack_UIFullPage:Count() > 0 then
				--目前已經開啟的 UI
				local _PreUI = _Stack_UIFullPage:Peek()
				--如果目前已經開啟的 UI 跟正要開啟的 UI 一樣 那就不用再 Push
				if _PreUI[1] == iUI then
					_Push = false
				end
				--如果需要 Push
				if _Push then
					if _IsNeedPreviousStack  or (_PreUI[2] and iIsForceUseArgs) then
						---將正在開啟的 UI 加入堆疊中
						_Stack_UIFullPage:Push({ iUI, iIsForceUseArgs, iParams })

						if _PreUI[1].m_UIStage == EUIStage.Opened then
							--如果 要關閉的介面 有控制其他介面關閉的話 Call 關閉
							if _PreUI[1].m_Table_VisibleControlOtherUI ~= nil and table.Count(_PreUI[1].m_Table_VisibleControlOtherUI) > 0 then
								this.CloseByTable(_PreUI[1].m_Table_VisibleControlOtherUI)
							end

							---關掉已經開啟的 UI
							_PreUI[1].m_UIStage = EUIStage.Closing
							--已經開啟的 UI 有 Close function
							if _PreUI[1].Close ~= nil then
								_PreUI[1].Close(iParams)
							end

							--已經開啟的 UI 是否有 關閉效果
							if _PreUI[1].OnCloseEffect ~= nil then
								_PreUI[1].OnCloseEffect()
							else
								this.SetUIPrefab(_PreUI[1], false)
							end

							this.RemoveUI(_PreUI[1])
						end

						_Push = false
					end
				end
			end
			---將目前要開啟的介面加入堆疊中
			if _Push then
				_Stack_UIFullPage:Push({ iUI, iIsForceUseArgs, iParams })
			end
		end
	end
--=========================================================================
	--關閉後的檢查
	if this.GetUIStage(iUI) == EUIStage.Closed then
		--如果有介面在等待介面關閉特效動作
		if not string.IsNullOrEmpty(UIBackGround_Controller.m_WaitFullPageCloseEffectUIName) then
			return
		end
		---堆疊的最上層 UI
		local _PreUI = nil
		local _IsNeedOpenNextStackUI = false
		if _Stack_UIFullPage:Count() > 0 then
			_PreUI = _Stack_UIFullPage:Peek()

			--如果現在關閉的是最上層 UI
			if _PreUI[1] == iUI then
				--要開啟下層 UI
				_IsNeedOpenNextStackUI = true
				--先將要關閉的 UI Pop 出來
				_Stack_UIFullPage:Pop()
			end
		end

		--如果有需要開啟前一個 UI
		if _IsNeedOpenNextStackUI then
			if _Stack_UIFullPage:Count() > 0 then
				---如果是直接回主介面
				if this.m_IsCloseBackToMain then
					---從堆疊內取得要開啟的 UI
					local _PreNextUI = _Stack_UIFullPage:Pop()
					if _Stack_UIFullPage:Count() >= 0 and _PreNextUI[1] ~= Main_Controller then
						--取出後 如果後面沒有 UI 且取出的 UI 也不是主介面
						if _Stack_UIFullPage:Count() == 0 and _PreNextUI[1] ~= Main_Controller then
							this.Open(UIBlackBase_Controller)
							this.Open(Main_Controller)
							this.m_IsCloseBackToMain = false
							return
						end
						--直接 Pop 到 Main_Controller
						while _PreNextUI[1] ~= Main_Controller do
							_PreNextUI = _Stack_UIFullPage:Pop()
							--取出後 如果後面沒有 UI 且取出的 UI 也不是主介面
							if _Stack_UIFullPage:Count() == 0 and _PreNextUI[1] ~= Main_Controller then
								this.Open(UIBlackBase_Controller)
								this.Open(Main_Controller)
								this.m_IsCloseBackToMain = false
								return
							end
						end
					end
					local _CurrentParams = _PreNextUI[3] ~= nil and _PreNextUI[3] or {}
					this.DoOpen(_PreNextUI[1], _PreNextUI[2], unpack(_CurrentParams, 1, table.maxn(_CurrentParams)))
					this.m_IsCloseBackToMain = false
				else
					---從堆疊內取得要開啟的 UI
					local _PreNextUI = _Stack_UIFullPage:Peek()
					---如果要關閉的UI 是需要啟動自動堆疊的
					local _CurrentIsNeedPreviousStack = _PreUI[1].m_IsAutomaticStackingFullPage ~= nil and _PreUI[1].m_IsAutomaticStackingFullPage
					if _CurrentIsNeedPreviousStack or (_PreUI[2] and _PreNextUI[2]) then
						--檢查開啟方法 定開啟 UI
						if _PreNextUI[2] then
							_Stack_UIFullPage:Pop()
							local _CurrentParams = _PreNextUI[3] ~= nil and _PreNextUI[3] or {}
							this.DoOpen(_PreNextUI[1], _PreNextUI[2], unpack(_CurrentParams, 1, table.maxn(_CurrentParams)))
						else
							if _PreNextUI[1].m_UIOrderLayerKey == EUIOrderLayers.FullPage  and  _PreNextUI[1].m_UIStage ~= EUIStage.LoadBGFinish then
								local _IsNeedLoadTexture = UIBackGround_Controller.SetUIBackground_ForUIMgr(_PreNextUI[1])
								if _IsNeedLoadTexture then
									return
								end
							end

							--設定階段 正在開啟中
							_PreNextUI[1].m_UIStage = EUIStage.Opening
							this.InsertUI(_PreNextUI[1])
							--檢查是否開啟成功
							local _IsOpen = OpenUI(_PreNextUI[1])
							if not _IsOpen then
								D.LogWarning( _PreNextUI[1].m_Name .. " open failed" )
								return
							elseif _IsOpen == nil then
								D.LogError(_PreNextUI[1].m_Name..".Open()，未回傳 bool 值")
							else
								if _PreNextUI[1].OnOpenEffect == nil then
									ToDoAfterOpenUISucceed(_PreNextUI[1])
								end
							end
						end
					end
				end
			end
		end
	end
end

---UI 關閉後要做甚麼事
local function DoAfterUIClosed(iUI)
	--是否要打開 HUD
	if IsCanSetHUDAndCtrlWalk(iUI, false) then
		SetHUDAndCtrlWalk(true)
	end

	---第一個事件是否在進行中
	local _IsFirstEventDoing, _CurrentControllerTable = this.IsFirstEventDoing()
	--如果第一個事件正在進行中
	if _IsFirstEventDoing then
		-- 如果第一個事件 是 正要關閉的 UI
		if not table.IsNullOrEmpty(_CurrentControllerTable) and iUI == _CurrentControllerTable[1] then
			--把第一筆丟出來
			_Queue_CurrentSaveUIOpenByEvent:Dequeue()
			--把正在關閉的事件 UI 標記結束
			iUI.m_IsDoingEvent = false
			---是否有事件要進行
			local _IsHaveEventUINeedOpen = this.IsHaveEventUINeedOpen()
			--丟出第一筆後 如果不是在過場中
			if not SceneMgr.m_IsLoadingScene then
				--如果還有其他事件要進行 且不在轉場中
				if _IsHaveEventUINeedOpen then
					OpenNextEventUI()
				else
					--傳送個事件下一步
					if iUI == TimeLine_Controller then
						TimeLine_Model.SendTimeLineEnd()
					elseif iUI == CGPlayer_Controller then
						SendProtocol_006._009()
					else
						SendProtocol_006._006()
					end
				end
			end
		end
	end

	--堆疊 開啟上個 UI
	if iUI.m_UIOrderLayerKey == EUIOrderLayers.FullPage then
		this.RemoveUI(iUI)
		--關全版 UI 先關閉所有的半版 UI
		this.CloseByTable(_EUIOrderTable[EUIOrderLayers.HalfPage_Left])
		this.CloseByTable(_EUIOrderTable[EUIOrderLayers.HalfPage_Right])
		this.CloseByTable(_EUIOrderTable[EUIOrderLayers.HalfPage_Center])
		--沒登入時 關閉的介面不是 Login 且不是在創角中的話 一定要開登入介面
		if not Login_Model.m_IsLogin then
			if iUI ~= Login_Controller and iUI ~= UIBlackBase_Controller
			and not ( SceneMgr.GetSceneCID() == CreateRoleSetting.m_CreateRoleSceneID or SceneMgr.GetSceneCID() == CreateRoleSetting.m_LabSceneID ) then
				UIMgr.Open(Login_Controller)
			end
			return
		end
		---是否還有排隊的 Event 要開 UI
		local _IsFirstEventDoing, _CurrentUITable = this.IsFirstEventDoing()
		if not _IsFirstEventDoing or ( _IsFirstEventDoing and iUI.m_UIOrderLayerKey == EUIOrderLayers.FullPage and _CurrentUITable[1].m_UIOrderLayerKey ~= EUIOrderLayers.FullPage ) then
			CheckUIStack(iUI)
		end
	end
	if iUI.DoAfterUIClosed ~= nil then
		iUI.DoAfterUIClosed(iUI)
	end
	UIBackGround_Controller.AfterUIEffectClosed()
end

--檢查有沒有允許開啟 UI 的永標
local function IsUICanOpenByStaticFlag(iUI)
	if this.m_IsUseUIStaticFlagCtrl then
		--檢查 UI 能不能開
		local _UISettingData = UISetting.GetUISettingData(iUI)
		--要開的時候再檢查
		if _UISettingData ~= nil then
			--檢查永標
			if _UISettingData.m_StaticFlagID ~= nil and _UISettingData.m_StaticFlagID > 0 and not PlayerData.IsHaveStaticFlag(_UISettingData.m_StaticFlagID) then
				D.Log(GString.GetTextWithColor("沒有永標，需求永標[".._UISettingData.m_StaticFlagID.."]，請檢查 UISetting.lua 是否設定正確", Color.Red))
				return false
			end
			--檢查等級
			if _UISettingData.m_OpenLv ~= nil and _UISettingData.m_OpenLv > 0 and PlayerData.GetLv() < _UISettingData.m_OpenLv then
				D.Log(GString.GetTextWithColor("等級不符，需求等級[".._UISettingData.m_OpenLv.."]，請檢查 UISetting.lua 是否設定正確", Color.Red))
				return false
			end
		else
			return false
		end
	end
	return true
end

---依照個介面開關類型 設定 UI Prefab
---@param iUI XXXController 個介面的 Controller
---@param iIsActive boolean 是否要顯示 Prefab
local function SetUIPrefabStateByType(iUI, iIsActive)
	--如果 UI 是第一次 從 ResourceMgr.Load 取出來的且還沒初始化位置。
	--在這裡做原因是希望要 UI 準備好之後才讓 UICamera 照到
	if not iUI.m_IsInitUIPosition and iUI.gameObject and iUI.gameObject.transform then
		iUI.gameObject.transform:SetParent(GetUIParent(iUI))
		iUI.gameObject.transform.localPosition = Vector3.zero
		iUI.gameObject.transform.localScale = Vector3.one
		iUI.gameObject.transform.localRotation = Quaternion.identity

		iUI.RectTransform.anchorMin = Vector2.zero
		iUI.RectTransform.anchorMax = Vector2.one
		iUI.RectTransform.anchoredPosition = Vector2.zero
		iUI.RectTransform.sizeDelta = Vector2.zero

		---此 UI 原本的位置
		iUI.m_OriginalAnchoredPosition = iUI.RectTransform.anchoredPosition
		iUI.m_IsInitUIPosition = true
	end

	---是否需要重設 Menu 條件判斷
	local _IsNeedResetMenu = Menu_Controller.UIInMenu(iUI) and (((IsHalfPage(iUI)) and this.IsVisible(Menu_Controller)) 
						or (not (IsHalfPage(iUI)) and not iIsActive))

	--重設Menu (必須是有在 Menu內的 UI)
	if _IsNeedResetMenu then
		if iIsActive then
			Menu_Controller.ResetWhenOtherUIOpen()
		end
		Menu_Controller.SwitchPermanentButtonInteractable(iUI.m_Index, not iIsActive)
	end

	--改變顯示狀態
	if iUI.m_OpenCloseType == EUIOpenCloseType.ChangeActive then
		iUI.gameObject:SetActive(iIsActive)
	--改變 Prefab 位置
	elseif iUI.m_OpenCloseType == EUIOpenCloseType.MoveOutOfCameraRange then
		if not iIsActive then
			iUI.RectTransform.anchoredPosition = _UIStorePlace
		else
			iUI.RectTransform.anchoredPosition = iUI.m_OriginalAnchoredPosition
		end
	--預設
	else
		iUI.gameObject:SetActive(iIsActive)
	end
end

local function CheckUIBound()
	if not this.m_IsUseBoundaryCtrl then
		this.m_UIBoundaryCtrl.HorizontalIndentationDistance = 0
		this.m_UIBoundaryCtrl.VerticalIndentationDistance = 0
		return
	end

	this.m_UIBoundaryCtrl.HorizontalIndentationDistance = this.GetIndentationDistanceInRange(this.m_UIBoundaryCtrl.HorizontalIndentationDistance)
	this.m_UIBoundaryCtrl.VerticalIndentationDistance = this.GetIndentationDistanceInRange(this.m_UIBoundaryCtrl.VerticalIndentationDistance)

	if (this.m_UIBoundaryCtrl.HorizontalIndentationDistance ~= this.GetCurrentHorizontalIndentationDistance()) or (this.m_UIBoundaryCtrl.VerticalIndentationDistance ~= this.GetCurrentVerticalIndentationDistance()) then
		this.SetUIIndentationDistance(this.m_UIBoundaryCtrl.HorizontalIndentationDistance, this.m_UIBoundaryCtrl.VerticalIndentationDistance)
	end
end
--endregion

---初始化
function UIMgr.Init()
	---UIMgr 物件本人
	this.gameObject = GameObject.FindGameObjectWithTag("UIMgr")
	---UIMgr 物件本人
	this.GObj_DebugConsole = GameObject.Find("GameTools.Log.D")
	---UIMgr 的 transform
	this.transform = this.gameObject.transform
	---UIMgr 的 Canvas
	this.m_Canvas = this.transform:GetComponent("Canvas")
	---UIMgr RectTransform
	this.m_RectTransform = this.transform:GetComponent("RectTransform")
	---Script UIBoundaryCtrl
	this.m_UIBoundaryCtrl = this.transform:GetComponent("UIBoundaryCtrl")
	---設定 UIBoundaryCtrl Delegate
	this.m_UIBoundaryCtrl.m_Action_CheckUIBound = System.Action(CheckUIBound)
	---Script Camera
	this.m_UICamera = this.gameObject.Find("UICamera"):GetComponent("Camera")
	---@type GlitchCtrl
	this.m_UICameraGlitch = this.m_UICamera and Extension.AddMissingComponent(this.m_UICamera.gameObject, typeof(GlitchCtrl)) or nil
	this.m_UICameraGlitch.enabled = false
	---Script CanvasScaler
	this.m_CanvasScaler = this.gameObject:GetComponent("CanvasScaler")
	---放 UI 的父物件 (全版)
	this.m_Transform_FullPage = this.gameObject.Find("FullPage").transform
	---放 UI 的父物件 (半版)
	this.m_Transform_HalfPage = this.gameObject.Find("HalfPage").transform
	---放 UI 的父物件 (最上層)
	this.m_Transform_Peak = this.gameObject.Find("Peak").transform
	---放 UI 的父物件 (Debug層)
	this.m_Transform_Debug = this.gameObject.Find("Debug").transform
	---背景圖片 RawImage
	this.m_RawImage_BG = this.gameObject.Find("UIBackGround"):GetComponent("RawImage")
	---背景圖片 RawImage 的UIAnimation
	this.m_RawImage_UIAnimation = this.gameObject.Find("UIBackGround"):GetComponent("UIAnimation")
	---背景圖片 Canvas
	this.m_Canvas_BG = this.m_RawImage_BG:GetComponent("Canvas")
	---背景黑色遮罩 Image
	this.m_Image_BGMask = this.gameObject.Find("Image_BackGroundMask"):GetComponent("Image")
	---背景黑色遮罩 Canvas
	this.m_Canvas_BGMask = this.gameObject.Find("BaseBlackBackGround"):GetComponent("Canvas")

	this.m_IsUseUIStaticFlagCtrl = not ProjectMgr.IsDebug()
	InitUIIndentationDistance()
	InitUIMatchWidthOrHeight()
	UIVisibleEffect.Init()
	UIMgr.SetUIStorePlace()

	---ExeCommonQuery_View 自己
	local _CurrentGameObject = this.m_Transform_Peak:GetComponent("Transform"):Find("ExeCommonQuery_View").gameObject
	-- 開遊戲就要準備好

	GameObject.Destroy(_CurrentGameObject)
end

---設定關閉後 UI 的移動位置
function UIMgr.SetUIStorePlace()
	local _Offset = 0
	if this.m_IsUseBoundaryCtrl then
		_Offset = this.GetCurrentHorizontalIndentationDistance() / 2
	end
	_UIStorePlace.x = Screen.width * 5 + _Offset
end

---取得 UI 目前階段
---@return EUIStage
function UIMgr.GetUIStage(iUI)
	if iUI.m_UIStage == nil then
		iUI.m_UIStage = EUIStage.None
	end
	return iUI.m_UIStage
end

---是否有需要開啟的 事件UI
---@return boolean , table table 的結構 {[1] = UIController, [2] = iParams}
function UIMgr.IsHaveEventUINeedOpen()
	return _Queue_CurrentSaveUIOpenByEvent:Size() > 0 , _Queue_CurrentSaveUIOpenByEvent:Peek()
end

---是否第一個事件正再啟動
---@return boolean , table table 的結構 {[1] = UIController, [2] = iParams}
function UIMgr.IsFirstEventDoing()
	local _IsFirstEventDoing = false
	--檢查第一筆事件的 UI
	local _CurrentUITable = _Queue_CurrentSaveUIOpenByEvent:Peek()
	if this.IsHaveEventUINeedOpen() and _CurrentUITable ~= nil then
		--檢查 是否可已啟用 UI
		if _CurrentUITable and _CurrentUITable[1].m_IsDoingEvent ~= nil and _CurrentUITable[1].m_IsDoingEvent then
			_IsFirstEventDoing = true
		end
	end
	return _IsFirstEventDoing , _CurrentUITable
end

---取得指定 UI 是否初始化完成
function UIMgr.IsInitialized(iUI)
	if iUI.m_UIStage == nil then
		return false
	else
		return iUI.m_UIStage >= EUIStage.Initialized
	end
end

function UIMgr.Update()
	UIVisibleEffect.Update()
	for key, value in pairs(_Table_UIInHierarchy) do
		if UIMgr.IsVisible(value) then
			if value.Update ~= nil then
				value.Update()
			end
		end
	end
	--檢查是否有 UI 等待關閉
	if table.Count(_Table_WaitUIOpendToClose) > 0 then
		for key, value in pairs(_Table_WaitUIOpendToClose) do
			if UIMgr.GetUIStage(value) == EUIStage.Opened then
				this.CloseToPreviousPage(value)
				table.removeByKey(_Table_WaitUIOpendToClose, key)
			end
		end
	end

end

function UIMgr.FixedUpdate()
	for key, value in pairs(_Table_UIInHierarchy) do
		if UIMgr.IsVisible(value) then
			if value.FixedUpdate ~= nil then
				value.FixedUpdate()
			end
		end
	end
end

---修正 UIAnimation 移動類型 影藏時 在調整 UI 邊界會出現
function UIMgr.UpdateAllUIAnimation()
	for key, value in pairs(_Table_UIInHierarchy) do
		if UIMgr.IsVisible(value) then
			if value.m_ViewRef ~= nil then
				--Dic_UIAnimation 數量大於 0 才需要動作
				if value.m_ViewRef.m_Dic_UIAnimation:Count() > 0 then
					---Dic_UIAnimation
					local _Dic_UIAnimation = value.m_ViewRef.m_Dic_UIAnimation
					---Dic_UIAnimation Keys
					local _Keys
					if _Dic_UIAnimation then
						---取 Dic_UIAnimation Keys
						_Keys = _Dic_UIAnimation:GetKeys()
						---開始檢查設定
						for i = 0, _Dic_UIAnimation:Count() - 1 do
							local _k = _Keys[i]
							local _UIAnimation = _Dic_UIAnimation:Get(_k)
							if not string.IsNullOrEmpty(_UIAnimation.m_AnimationTypeClose) then
								local _CurrentType = UIVisibleEffect.GetUIVisibleEffectData(_UIAnimation.m_AnimationTypeClose).m_EffectKind
								if _CurrentType >= AnimationEffectKind.MoveFromLeftBorder
									and _CurrentType <= AnimationEffectKind.MoveFromDownBorder and not _UIAnimation.LastDoAnimationState then
										_UIAnimation:DoActive(_UIAnimation.LastDoAnimationState)
								end
							end
						end
					end
				end
			end
		end
	end
end

---檢查UI是否顯示
---@param iUI table 要顯示的 UI
function UIMgr.IsVisible(iUI)
	--沒有要檢查的 UI
	if iUI == nil then
		return false
	end

	--要檢查的 UI 沒有 gameObject
	if iUI.gameObject == nil then
		return false
	end
	local _CurrentUIStage = UIMgr.GetUIStage(iUI)

	return _CurrentUIStage == EUIStage.Opened
end

function UIMgr.InsertUI(iUI)
	if not iUI.m_IsAutomaticStackingFullPage and table.Contains(_ScreenView, iUI) then
		return
	end

	table.insert(_ScreenView, iUI)

	if #_ScreenView == 0 then
		return
	end
end

function UIMgr.RemoveUI(iUI)
	if table.Contains(_Table_ConnotDestroy, iUI) then
		return
	end

	local _Count = table.maxn(_ScreenView)
	for i = _Count, 1, -1 do
		if _ScreenView[i] == iUI then
			table.remove(_ScreenView, i)
			if iUI.m_IsAutomaticStackingFullPage then
				break
			end
		end
	end

	if #_ScreenView == 0 then
		return
	end
end

function UIMgr.SetUIPrefab(iUI, iIsOpen)
	if iUI == nil or iUI.gameObject == nil then
		return
	end

	--檢查 UI 開關
	if iIsOpen == nil then
		iIsOpen = not iUI.gameObject.activeSelf
	end

	--如果是關閉的時後有事件 UI 需要開啟
	if not iIsOpen and _EventUIWaitOpenWhenLoadingUIClose ~= nil then
		_EventUIWaitOpenWhenLoadingUIClose = nil
		OpenNextEventUI()
	end

	--設定 UI 開關
	SetUIPrefabStateByType(iUI, iIsOpen)

	-- 要開的時候才需要檢查有沒有永標 及 更新 UI Order
	if iIsOpen then
		--更新UI Order
		SetUIOrder(iUI)
	end

	if iIsOpen then
		CheckUIComponentsVisible(iUI)
	end

	--執行顯示切換時要做的事 UI Initializing 時有呼叫關閉
	if iUI.OnVisible ~= nil and this.GetUIStage(iUI) > EUIStage.Initialized then
		iUI.OnVisible(iIsOpen)
	end

	--關閉時才需要的設定
	if not iIsOpen then
		if UIMgr.GetUIStage(iUI) == EUIStage.DoingCloseEffect or UIMgr.GetUIStage(iUI) == EUIStage.Closing then
			--改階段 已關閉介面
			iUI.m_UIStage = EUIStage.Closed
			--關閉完成後要做甚麼事
			DoAfterUIClosed(iUI)
		end
	end
end

---更新指定 UI 的 Order
---@param iUI table XXX_Controller
function UIMgr.UpdateUIOrderByUIController(iUI)
	SetUIOrder(iUI)
end

---開啟UI
---@param iUI table 要開啟的 UI
---Ex: function (xxx_Controller, a, b, c) =>
---a,b,c = iParam[] => iParam[1] = a, iParam[2] = b, iParam[3] = c  以此類推
function UIMgr.Open(iUI, ...)

	if UIMgr.IsInDelayyUITable(iUI) then
		local args = { ... }

		local _DoOpen = function()
			this.DoOpen(iUI, false, args)
		end

		UIMgr.AddDelegateOpenUI(iUI,_DoOpen)

		if _Cur_Opened_WaitingUI == nil then
			UIMgr.SetAllowFastClickHint_DelayUITable(false)
			UIMgr.TriggerDelegateOpenUI()
		end

	else
		this.DoOpen(iUI, false, { ... })
	end
end

---用 UIIndex 開啟 UI
---@param iUIIndex number 要開啟的 UIIndex
---Ex: function (UIIndex, a, b, c) =>
---a,b,c = iParam[] => iParam[1] = a, iParam[2] = b, iParam[3] = c  以此類推
function UIMgr.OpenByIndex(iUIIndex, ...)
	local _UIController = UISetting.GetUIControllerByUIIndex(iUIIndex)
	if _UIController ~= nil then
		this.DoOpen(_UIController, false, { ... })
	else
		local _String = "UIMgr.OpenByIndex，找不到可開啟的 UIController，UIIndex："..iUIIndex
		D.Log(GString.GetTextWithColor(_String , Color.Red))
	end
end

---檢查並決定 開/關 UI (單個按鈕開關介面用)
---@param iUIIndex number 需要開關的 UI
---@return UIController 取得介面的 Controller
function UIMgr.CheckAndSetUIVisible(iUIIndex, ...)
	local _UIController = UISetting.GetUIControllerByUIIndex(iUIIndex)
	if _UIController ~= nil then
		if not UIMgr.IsVisible(_UIController) then
			this.DoOpen(_UIController, false, { ... })
			return _UIController
		else
			this.CloseToPreviousPage(_UIController, ...)
			return _UIController
		end
	else
		local _String = "UIMgr.CheckAndSetUIVisible，找不到可以開關的 UIController，UIIndex："..iUIIndex
		D.Log(GString.GetTextWithColor(_String , Color.Red))
		return _UIController
	end
end

function UIMgr.DoOpen(iUI, iIsForceUseArgs, iArgs)
	if iUI == nil then
		return
	end

	if _Table_UI[iUI.m_UIView] == nil then
		this.Preload(iUI, true, iIsForceUseArgs, iArgs)
		return
	end

	--檢查 是否可以 開啟
	if UIMgr.GetUIStage(iUI) < EUIStage.Initialized  then
		return
	end

	--檢查有沒有永標 能不能開介面
	if UISetting.IsUIHaveIndex(iUI) then
		local iIsCanOpen = IsUICanOpenByStaticFlag(iUI)
		--沒有永標 關介面
		if not iIsCanOpen then
			return
		end
	end

	--檢查 目前 UI 有開啟 且 在播劇情動畫 不讓開任何 的 UI
	if this.m_OpenedPlotAnimationUI ~= nil and UIMgr.IsInitialized(this.m_OpenedPlotAnimationUI) and UIMgr.IsVisible(this.m_OpenedPlotAnimationUI) and iUI ~= this.m_OpenedPlotAnimationUI then
		---現在正在開啟 Loading UI
		local _IsOpenLoadingNow = this.m_CloseLoadingWhenUIOpen ~= nil and iUI == GetCurrentLoadingUI()
		---TimeLine 還沒播放完畢
		local _IsTimeLinePlaying = this.m_OpenedPlotAnimationUI == TimeLine_Controller and not TimeLine_Model.GetCurrentTimeLine().isPlayed
		---CG 播放中
		local _IsCGPalying = this.m_OpenedPlotAnimationUI == CGPlayer_Controller and CGPlayer_Controller.IsCGPlaying()
		--TimeLine 或是 CG 正在播放中
		if _IsTimeLinePlaying or _IsCGPalying then
			--現在正在開啟 Loading UI
			if _IsOpenLoadingNow then
				--設定至 等待開啟 Loading 的 table
				_WaitOpenLoading[1] = iUI
				_WaitOpenLoading[2] = iArgs
			end
			return
		end
	end
	--全版介面要準備BG
	if iUI.m_UIOrderLayerKey == EUIOrderLayers.FullPage  and  iUI.m_UIStage ~= EUIStage.LoadBGFinish then
		local _IsNeedLoadTexture = UIBackGround_Controller.SetUIBackground_ForUIMgr(iUI,iArgs)
		if _IsNeedLoadTexture then
			return
		end
	end

	--改階段 正在開啟中
	iUI.m_UIStage = EUIStage.Opening

	--是否需要關閉現在開啟的 UI
	if IsNeedCloseCurrentOpenAndOpenNextEventUI(iUI, iIsForceUseArgs, iArgs) then
		return
	end

	if iUI.m_UIOrderLayerKey == EUIOrderLayers.FullPage then
		this.InsertUI(iUI)
		this.CloseByTable(_EUIOrderTable[EUIOrderLayers.HalfPage_Left], Main_Model.EUIINMain)
		this.CloseByTable(_EUIOrderTable[EUIOrderLayers.HalfPage_Right], Main_Model.EUIINMain)
		this.CloseByTable(_EUIOrderTable[EUIOrderLayers.HalfPage_Center], Main_Model.EUIINMain)
		--在轉場時 或是 登入前初始化時 不用關閉 Loading 相關
		if SceneMgr.m_IsLoadingScene or (not Login_Model.m_IsLogin and not DataMgr.IsAllDataInitialized()) then
			for key, value in pairs(_Table_Loading) do
				table.insert(_ExpceptClosePeakTableWhenUIOpen, value)
			end
		end
		this.CloseByTable(_EUIOrderTable[EUIOrderLayers.Peak], _ExpceptClosePeakTableWhenUIOpen)
		--檢查堆疊
		CheckUIStack(iUI, iIsForceUseArgs, iArgs)
	end

	local _IsOpen = OpenUI(iUI, iArgs)
	if not _IsOpen then
		D.LogWarning( iUI.m_Name .. " open failed" )
		return
	elseif _IsOpen == nil then
		D.LogError(iUI.m_Name..".Open()，未回傳 bool 值")
	else
		if iUI.OnOpenEffect == nil then
			ToDoAfterOpenUISucceed(iUI)
		end
	end
end

---預載UI資源
function UIMgr.Preload(iUI, iIsNeedOpen, iIsForceUseArgs, iArgs)
	if _Table_UI[iUI.m_UIView] == nil then
		_Table_UI[iUI.m_UIView] = iUI
		---檢查 UI View 是不是已經在場上
		local _UITransform = GetUIParent(iUI):Find(iUI.m_UIView)
		if iUI.m_UIStage == nil then
			---初始化階段
			iUI.m_UIStage = EUIStage.None
		end

		---判斷是否已讀取資源 或是 UI 已在場上
		if UIMgr.GetUIStage(iUI) == EUIStage.UIObjLoaded or _UITransform ~= nil then
			--UI 已在場上 直接設定成已 Load 完資源
			if _UITransform ~= nil then
				iUI.m_UIStage = EUIStage.UIObjLoaded
				_Table_UIInHierarchy[iUI.m_UIView] = iUI
			end
			--已讀取資源 進入初始化
			this.InitUI(iUI, _UITransform.gameObject, iIsNeedOpen, iIsForceUseArgs, iArgs)
			return _Table_UI[iUI.m_UIView]
		--還沒讀取到 進入讀取資源
		else
			--如果在正取資源 跳掉
			if UIMgr.GetUIStage(iUI) == EUIStage.LoadingFromResourceMgr then
				return
			end

			iUI.m_UIStage = EUIStage.LoadingFromResourceMgr

			--有要控制其他 UI 先預載
			if table.Count(iUI.m_Table_VisibleControlOtherUI) > 0 then
				for key, value in pairs(iUI.m_Table_VisibleControlOtherUI) do
					this.Preload(value, false, false)
				end
			end

			ResourceMgr.Load(iUI.m_UIView, function(iAsset)
				if _Table_UIInHierarchy[iUI.m_UIView] ~= nil then
					return _Table_UI[iUI.m_UIView]
				end

				local _UIObj = iAsset
				if Extension.IsUnityObjectNull(_UIObj) then
					D.LogError(string.Concat(iUI.m_UIView, ": can`t found asset"))
					return
				end

				---階段 改讀物件取完成
				iUI.m_UIStage = EUIStage.UIObjLoaded

				_Table_UIInHierarchy[iUI.m_UIView] = iUI

				this.InitUI(iUI, _UIObj, iIsNeedOpen, iIsForceUseArgs, iArgs)
				return _Table_UI[iUI.m_UIView]
			end)
		end
	else
		return _Table_UI[iUI.m_UIView]
	end
end

function UIMgr.InitUIVisibleEffect(iUI)
	InitUIVisibleEffect(iUI)
end

---進行UI初始化
function UIMgr.InitUI(iUI, iUIObj, iIsNeedOpen, iIsForceUseArgs, iArgs)
	--檢查 是否可以初始化
	if UIMgr.GetUIStage(iUI) < EUIStage.UIObjLoaded  then
		return
	end

	--改階段 正在初始化中
	iUI.m_UIStage = EUIStage.Initializing

	if iUI.InitBase ~= nil then
		iUI:InitBase(iUIObj)
	end

	--改階段 初始已完成
	iUI.m_UIStage = EUIStage.Initialized

	if iIsNeedOpen == true then
		this.DoOpen(iUI, iIsForceUseArgs, iArgs)
	end
end

---用 UIIndex 關閉 UI
---@param iUIIndex number 要關閉的 UIIndex
---Ex: function (UIIndex, a, b, c) =>
---a,b,c = iParam[] => iParam[1] = a, iParam[2] = b, iParam[3] = c  以此類推
function UIMgr.CloseByIndex(iUIIndex, ...)
	local _UIController = UISetting.GetUIControllerByUIIndex(iUIIndex)
	if _UIController ~= nil then
		this.CloseToPreviousPage(_UIController, ... )
	else
		local _String = "UIMgr.CloseByIndex，找不到可關閉的 UIController，UIIndex："..iUIIndex
		D.Log(GString.GetTextWithColor(_String , Color.Red))
	end
end

---Close to Main
function UIMgr.Close(iUI, ...)
	---全版才有用到他
	if iUI.m_UIOrderLayerKey == EUIOrderLayers.FullPage then
		this.m_IsCloseBackToMain = true
	end
	this.CloseToPreviousPage(iUI, ...)

	if UIMgr.IsInDelayyUITable(iUI) then
		UIMgr.TriggerDelegateOpenUI()
	end
end

---回到上一頁
function UIMgr.CloseToPreviousPage(iUI, ...)
	if iUI == nil then
		return
	end

	local _Args = ...

	--檢查 是否需要等待關閉
	if _Table_UI[iUI.m_UIView] ~= nil and UIMgr.GetUIStage(iUI) > EUIStage.Initialized and UIMgr.GetUIStage(iUI) < EUIStage.Opened then
		if not table.Contains(_Table_WaitUIOpendToClose, iUI) then
			_Table_WaitUIOpendToClose[iUI.m_UIView] = iUI
		end
		return
	elseif _Table_UIInHierarchy[iUI.m_UIView] == nil or UIMgr.GetUIStage(iUI) == EUIStage.None then
		return
	end

	--改階段 正在關閉中
	iUI.m_UIStage = EUIStage.Closing

	CloseUI(iUI, _Args)
end

---開啟指定UI們
---@param iNeedOpenUITable table 需要開啟的 UI 們
function UIMgr.OpenByTable(iNeedOpenUITable)
	if iNeedOpenUITable ~= nil then
		for i = 1, table.Count(iNeedOpenUITable) do
			UIMgr.Open(iNeedOpenUITable[i])
		end
	end
end

---關閉指定UI們
---@param iNeedCloseUITable table 需要關閉的 UI 們
---@param iExceptTable table 指定的例外清單
function UIMgr.CloseByTable(iNeedCloseUITable, iExceptTable)
	for key, value in pairs(iNeedCloseUITable) do
		---檢查能不能關閉此介面 是否有在例外清單內
		local _IsCannotClose = (iExceptTable ~= nil and table.Contains(iExceptTable, value) )
		---檢查是否需要關閉此 UI
		if value ~= nil and not _IsCannotClose and UIMgr.IsVisible(value) then
			UIMgr.CloseToPreviousPage(value)
		end
	end
end

--region 關閉半版 Added by 凌傑RM#117450 2025.0107
function UIMgr.CloseHalfPageByMain()
	this.CloseByTable(_EUIOrderTable[EUIOrderLayers.HalfPage_Left], Main_Model.EUIINMain)
	this.CloseByTable(_EUIOrderTable[EUIOrderLayers.HalfPage_Right], Main_Model.EUIINMain)
	this.CloseByTable(_EUIOrderTable[EUIOrderLayers.HalfPage_Center], Main_Model.EUIINMain)
end
--endregion

---關閉所有頁面
function UIMgr.CloseAll(iExceptTable)
	for key, value in pairs(_Table_UI) do
		---檢查能不能關閉此介面 是否有在例外清單內
		local _IsCannotClose = (iExceptTable ~= nil and table.Contains(iExceptTable, value) )
		---檢查是否需要關閉此 UI
		if value ~= nil and not _IsCannotClose and UIMgr.IsVisible(value) then
			UIMgr.CloseToPreviousPage(value)
		end
	end
end

---所有 UI 登出要做甚麼
function UIMgr.CallAllLoggingOutToDo()
	for key, value in pairs(_Table_UI) do
		if value.LoggingOutToDo ~= nil then
			value.LoggingOutToDo()
		end
	end
end

---依名稱取得UI本人
---@param iUIName string UI 名稱 (包不包含 _View 都可以)
function UIMgr.GetUIByName(iUIName)
	if not string.match(iUIName, _String_ViewBack) then
		iUIName = iUIName .. _String_ViewBack
	end
	if _Table_UI[iUIName] ~= nil then
		return _Table_UI[iUIName]
	end
end

---檢查 UI 顯示狀態
function UIMgr.CheckAllUIVisible()
	--檢查各UI 要不要開
	for _UIDatakey, _UIData in pairs(UISetting.m_UISettingData) do
		if (not this.m_IsUseUIStaticFlagCtrl or PlayerData.IsHaveStaticFlag(_UIData.m_StaticFlagID))then
			if ProjectMgr.IsDebug() and not this.m_IsUseUIStaticFlagCtrl and _UIData.m_StaticFlagID > 0 and not PlayerData.IsHaveStaticFlag(_UIData.m_StaticFlagID) then
				if table.Contains(Main_Model.EUIINMain, _UIData.m_UI) then
					UIMgr.Open(_UIData.m_UI)
				end
			else
				if table.Contains(Main_Model.EUIINMain, _UIData.m_UI) then
					UIMgr.Open(_UIData.m_UI)
				end
			end
		else
			if this.m_IsUseUIStaticFlagCtrl and _UIData.m_StaticFlagID > 0 and not PlayerData.IsHaveStaticFlag(_UIData.m_StaticFlagID)then
				if table.Contains(Main_Model.EUIINMain, _UIData.m_UI) then
					UIMgr.CloseToPreviousPage(_UIData.m_UI)
				end
			end
		end
	end

	Menu_Controller.UpdateAllButtonsState()

	--檢查各UI 內的元件要不要開
	for key, value in pairs(_Table_UIInHierarchy) do
		if this.IsVisible(value) then
			CheckUIComponentsVisible(value)
		end
	end
end

---設定 UI 單個元件顯示狀態
---@param iUI xxx_Controller 哪個 UI 本人
---@param iComponentName string 包含&符號的元件名稱
---@param iComponentVisibleData ComponentVisibleData 元件顯示資料 可以在 UISetting.lua 內找到
function UIMgr.SetUIComponentVisible(iUI, iComponentName, iComponentVisibleData)
	if UIMgr.GetUIStage(iUI) < EUIStage.Initialized then
		return
	end
	if iUI.m_ViewRef == nil then
		D.LogError("UI：["..iUI.m_UIController.."] m_ViewRef is nil")
		return
	end

	local _TransComponent = iUI.m_ViewRef.m_Dic_Trans:Get(iComponentName)
	if _TransComponent == nil then
		D.LogError("無法在"..iUI.."內找到元件:"..iComponentName.."請確認 UISetting.lua 內相對應 UI 及元件資料")
	end
	local _GObjComponent = iUI.m_ViewRef.m_Dic_Trans:Get(iComponentName).gameObject
	--有設定的物件 才做判斷
	if iComponentVisibleData ~= nil and _GObjComponent ~= nil then
		local _CurrentUIStage = UIMgr.GetUIStage(iUI)
		--該 UI 有顯示 才做判斷
		if _CurrentUIStage >= EUIStage.Opening and _CurrentUIStage <= EUIStage.Opened then
			--有開永標判斷
			if  this.m_IsUseUIStaticFlagCtrl then
				---永標符合
				local _IsFlagMeet = iComponentVisibleData.m_StaticFlagID == nil
					or (iComponentVisibleData.m_StaticFlagID == -1 and iComponentVisibleData.m_EShowTypeWithNoFlag == EShowTypeWithNoFlag.Function)
					or ( iComponentVisibleData.m_StaticFlagID > 0 and PlayerData.IsHaveStaticFlag(iComponentVisibleData.m_StaticFlagID) )
				---等級符合
				local _IsLvMeet = iComponentVisibleData.m_OpenLv == nil or ( iComponentVisibleData.m_OpenLv >= 0 and PlayerData.GetLv() >= iComponentVisibleData.m_OpenLv )
				---元件顯示 永標跟等級都符合 才會顯示
				local _ComponentVisiable = _IsFlagMeet and _IsLvMeet
				--顯示方式
				if iComponentVisibleData.m_EShowTypeWithNoFlag == EShowTypeWithNoFlag.Default then
					_GObjComponent:SetActive(_ComponentVisiable)
				elseif iComponentVisibleData.m_EShowTypeWithNoFlag == EShowTypeWithNoFlag.Disable then
					if not _ComponentVisiable then
						_GObjComponent:SetEnable()
					else
						_GObjComponent:SetDisable()
					end
				elseif iComponentVisibleData.m_EShowTypeWithNoFlag == EShowTypeWithNoFlag.Function then
					if iComponentVisibleData.m_FunctionComponentVisible ~= nil then
						iComponentVisibleData.m_FunctionComponentVisible(_GObjComponent, iComponentVisibleData, _ComponentVisiable)
					else
						D.LogError("UI:"..iUI.m_Name.." 的元件["..iComponentName.."]永標時的表現形態為[2]，但沒有設定其需要操作的 Function ，請確認其類型或是補上需要實作的 Function")
					end
				end
			else
				--顯示方式
				if iComponentVisibleData.m_EShowTypeWithNoFlag == EShowTypeWithNoFlag.Default and not _GObjComponent.activeSelf then
					_GObjComponent:SetActive(true)
				elseif iComponentVisibleData.m_EShowTypeWithNoFlag == EShowTypeWithNoFlag.Disable then
					_GObjComponent:SetEnable()
				elseif iComponentVisibleData.m_EShowTypeWithNoFlag == EShowTypeWithNoFlag.Function and iComponentVisibleData.m_StaticFlagID ~= nil and iComponentVisibleData.m_StaticFlagID > 0 then
					if iComponentVisibleData.m_FunctionComponentVisible then
						iComponentVisibleData.m_FunctionComponentVisible(_GObjComponent, iComponentVisibleData, true)
					else
						D.LogError("UI:"..iUI.m_Name.." 的元件["..iComponentName.."]永標時的表現形態為[2]，但沒有設定其需要操作的 Function ，請確認其類型或是補上需要實作的 Function")
					end
				end
			end
		end
	else
		if _GObjComponent == nil then
			D.LogError("找不到設定的物件名稱，請檢查 UISetting.m_UISettingData["..UISetting.GetUISettingDataKey(iUI).."].m_ComponentsVisibleData["..iComponentName.."]，的名稱是否設定錯誤")
		end
	end
end

-- 顯示 icon 名子
function UIMgr.OpenIconName(iIsOpen, iIcon)
	if UIOrderTop_Controller and UIMgr.IsVisible(UIOrderTop_Controller) then
		UIOrderTop_Controller:ShowIconName(iIsOpen, iIcon == nil and "" or iIcon.m_Name, iIcon)
	end
end

---依照不同類型開啟 Loading 介面，
---@param iType LodingDefultParam Loading 開啟類型
---@param iCloseLoadingWhenthisUIOpen XXX_Controller 開啟甚麼 UI 時要關閉 Loading
---如果 [iType = ELoadingOpenType.None 時 不定參數 = iLoadingOpenedCallBack],
---如果 [iType = ELoadingOpenType.Defult 時 不定參數 = iStringTitle, iStringDescription, iIsNeedSlider, iLoadingOpenedCallBack],
---如果 [iType = ELoadingOpenType.CaptionTheater 時 不定參數 = iiTransitionEffectID, iLoadingOpenedCallBack]
---其他可以參考 此 functon 內 各 ELoadingOpenType 的順序
function UIMgr.OpenLoading(iType, iCloseLoadingWhenthisUIOpen, ...)
	local _Args = {...}
	this.m_NowLoadingOpenType = iType
	this.m_CloseLoadingWhenUIOpen = iCloseLoadingWhenthisUIOpen
	if iType == ELoadingOpenType.None then
		--20240529 鼎翰 不開 UI 直接執行過場
		_Args[1]()
	elseif iType == ELoadingOpenType.Defult then
		if _Table_UI[Loading_Controller.m_UIView] == nil then
			this.Preload(Loading_Controller, true, false, _Args)
			return
		else
			Loading_Controller.SetDefultBeforeOpen(_Args)
		end
	elseif iType == ELoadingOpenType.CaptionTheater then
		this.Open(CaptionTheater_Controller, ...)--iCaptionTheaterDataIndex 打字機資料的 Index
	elseif iType == ELoadingOpenType.Effect then
		-- body
		_Args[1]()
	elseif iType == ELoadingOpenType.UseCG then
		this.Open(CGPlayer_Controller, ...)
	end
		UIMgr.CloseCur_Opened_WaitingUIWhenLoading()
end

---過場時關閉目前開啟的 _Cur_Opened_WaitingUI
function UIMgr.CloseCur_Opened_WaitingUIWhenLoading()
	if SceneMgr.m_IsLoadingScene and _Cur_Opened_WaitingUI ~= nil then
		UIMgr.Close(_Cur_Opened_WaitingUI)
		_Cur_Opened_WaitingUI = nil
		UIMgr.SetAllowFastClickHint_DelayUITable(true)
	end
end

---關閉目前 Loading 的 UI
function UIMgr.CloseCurrentLoadingUI()
	--取得需要關閉的 UI
	local _CurrentUI = GetCurrentLoadingUI()
	if _CurrentUI ~= nil then
		this.CloseToPreviousPage(_CurrentUI)
	end
end

---事件開啟 UI 專用
function UIMgr.OpenUIByEvent(iUI, ...)
	if iUI.m_IsDoingEvent == nil then
		iUI.m_IsDoingEvent = false
	end

	---參數們
	local _Params = {...}
	---經由協定 開啟 TimeLine 時 補參數
	if iUI == TimeLine_Controller and table.Count(_Params) == 3 then
		_Params[table.Count(_Params) + 1] = nil
		_Params[table.Count(_Params) + 1] = nil
		_Params[table.Count(_Params) + 1] = false
	end
	--- 全部都要加入 Queue 內
	_Queue_CurrentSaveUIOpenByEvent:Enqueue({ iUI, _Params })

	--正在過場中先跳掉
	if SceneMgr.m_IsLoadingScene then
		return
	end

	local _IsFirstEventDoing = this.IsFirstEventDoing()

	--沒在過場中 但是 有事件在進行中  先跳掉
	if _IsFirstEventDoing then
		return
	end

	--全部都過惹~~ 開事件
	OpenNextEventUI()
end

---關閉動畫 UI
function UIMgr.ClosePlotAnimationUI()
	if this.m_OpenedPlotAnimationUI ~= nil and  this.m_OpenedPlotAnimationUI.m_IsWaitNextEventClose then
		UIMgr.CloseToPreviousPage(this.m_OpenedPlotAnimationUI)
	end
end

function UIMgr.SetBoundaryCtrlOn(iIsOn)
	this.m_IsUseBoundaryCtrl = iIsOn
	if iIsOn then
		UIMgr.SetUIIndentationDistance(this.m_UIIndentationDistance.Horizontal,this.m_UIIndentationDistance.Vertical)
		if ProjectMgr.IsDebug() then
			D.Log(GString.GetTextWithColor("GoldFinger 邊界內縮 已開啟", Color.Red))
		end
	else
		if ProjectMgr.IsDebug() then
			D.Log(GString.GetTextWithColor("GoldFinger 邊界內縮 已關閉", Color.Red))
		end
	end
	UIMgr.SetUIStorePlace()
end

---設定 UI 內縮距離 改變數值 需要用此 Function
---@param iHorizontalIndentationDistance float 橫向內縮距離(Left + Right)
---@param iVerticalIndentationDistance float 縱向內縮距離(Bottom)
function UIMgr.SetUIIndentationDistance(iHorizontalIndentationDistance,iVerticalIndentationDistance)
	if ProjectMgr.IsDebug() and not this.m_IsUseBoundaryCtrl then
		D.LogWarning(GString.GetTextWithColor("邊界內縮 未開啟，要縮放請到金手指內開啟", Color.Red))
		return
	end
	--內縮距離(寬)控制用
	local _HorizontalIndentationDistance = UIMgr.GetIndentationDistanceInRange(iHorizontalIndentationDistance)
	--內縮距離(高)控制用
	local _VerticalIndentationDistance = UIMgr.GetIndentationDistanceInRange(iVerticalIndentationDistance)

	--設定到 UIBoundaryCtrl 顯示改變後的距離
	if this.m_UIBoundaryCtrl.HorizontalIndentationDistance ~= _HorizontalIndentationDistance then
		this.m_UIBoundaryCtrl.HorizontalIndentationDistance = _HorizontalIndentationDistance
	end
	if this.m_UIBoundaryCtrl.VerticalIndentationDistance ~= _VerticalIndentationDistance then
		this.m_UIBoundaryCtrl.VerticalIndentationDistance = _VerticalIndentationDistance
	end

	--設定到 UIIndentationDistance 顯示改變後的距離
	if this.m_UIIndentationDistance.Horizontal ~= _HorizontalIndentationDistance then
		this.m_UIIndentationDistance.Horizontal = _HorizontalIndentationDistance
	end
	if this.m_UIIndentationDistance.Vertical ~= _VerticalIndentationDistance then
		this.m_UIIndentationDistance.Vertical = _VerticalIndentationDistance
	end

	UIMgr.UpdateAllUIAnimation()

	ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Device,"m_UIIndentationDistance", this.m_UIIndentationDistance)

	for key, value in pairs(_Table_UIInHierarchy) do
		if value.m_ViewRef ~= nil then
			this.SetUIBound(value)
		end
	end
end

---設定 UI 元件縮放比
function UIMgr.SetMatchWidthOrHeight(iValue)
	--改設定
	if not this.m_MatchWidthOrHeight.IsDeviceAlreadySet then
		this.m_MatchWidthOrHeight.IsDeviceAlreadySet = true
	end
	--設定數值
	this.m_MatchWidthOrHeight.Value = iValue
	this.m_CanvasScaler.matchWidthOrHeight = iValue
	ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Device,"m_MatchWidthOrHeight", this.m_MatchWidthOrHeight)
end

---取正確內縮距離 UIBoundaryCtrl.cs 會用
---@param iDistance float 要檢查的距離
---@return _Distance 檢查過後的正確距離
function UIMgr.GetIndentationDistanceInRange(iDistance)
	local _Distance = 0
	if iDistance > MAX_INDENTATION_DISTANCE then
		_Distance = MAX_INDENTATION_DISTANCE
	else
		_Distance = iDistance
	end
	return _Distance
end

---取 UIMgr 橫向內縮距離 UIBoundaryCtrl.cs 會用
---@return this.m_UIIndentationDistance.Horizontal 橫向內縮距離
function UIMgr.GetCurrentHorizontalIndentationDistance()
	return this.m_UIIndentationDistance.Horizontal
end

---取 UIMgr 縱向內縮距離 UIBoundaryCtrl.cs 會用
---@return this.m_UIIndentationDistance.Vertical 縱向內縮距離
function UIMgr.GetCurrentVerticalIndentationDistance()
	return this.m_UIIndentationDistance.Vertical
end

---取得動態調整的高
local function GetCurrentAdjustHeight(iUI)
	---調整後的 Height
	local _CurrentAdjustHeight = 0
	---介面 MainPanel 的 RectTransform
	local _MainPanelRectTransform = iUI.m_UIBoundaryTarget.RectTransform
	--不是不需要調整高度的 UI 也不是 主介面上的 UI 則 計算高度更新
	if not table.Contains(m_Table_UIDoNotAdjustHeigth,iUI) and not table.Contains(Main_Model.EUIINMain, iUI) then
		local _Ratio = this.m_RectTransform.rect.width / this.m_RectTransform.rect.height
		---檢查是否需要調整
		local _NeedAdjust = true
		---檢查比例是否為需要調整的比例
		for k,v in pairs(EResolutionStyleValue) do
			if math.abs(_Ratio - v) < 0.01 and (k == EResolutionStyle.Default or k == EResolutionStyle.Wide) then
				_NeedAdjust = false
				if ProjectMgr.IsEditor() then
					_MainPanelRectTransform.anchoredPosition = Vector2.zero
					_MainPanelRectTransform:SetSizeWithCurrentAnchors(Axis.Vertical, this.m_RectTransform.rect.height)
					break
				end
			end
		end
		---所需要減少的高
		--計算剩餘的高
		if _NeedAdjust and this.m_RectTransform.rect.height > this.m_CanvasScaler.referenceResolution.y then
			this.m_Canvas.ForceUpdateCanvases()
			_ReduceHeight = this.m_RectTransform.rect.height - this.m_CanvasScaler.referenceResolution.y
			_CurrentAdjustHeight = this.m_RectTransform.rect.height - _ReduceHeight
		end
		--如果要要調整 MainPanel 的 位置則需要下移 二分之一的 調整值
		if _CurrentAdjustHeight > 0 then
			if _CurrentAdjustHeight ~= _MainPanelRectTransform.rect.height then
				_MainPanelRectTransform.anchoredPosition = Vector2(_MainPanelRectTransform.anchoredPosition.x, - _ReduceHeight / 2 )
			end
		end

		--玩家沒有設定 則套用預設值
		--改設定
		if not this.m_MatchWidthOrHeight.IsDeviceAlreadySet then
			this.m_MatchWidthOrHeight.IsDeviceAlreadySet = true
			if  _CurrentAdjustHeight > 0  then
				UIMgr.SetMatchWidthOrHeight(EMatchWidthOrHeight.Width)
			else
				UIMgr.SetMatchWidthOrHeight(EMatchWidthOrHeight.Height)
			end
		else
			UIMgr.SetMatchWidthOrHeight(this.m_MatchWidthOrHeight.Value)
		end
	end
	--如果都沒調整 高設為原本的值
	if _CurrentAdjustHeight == 0 then
		_CurrentAdjustHeight = this.m_RectTransform.rect.height
	end

	return _CurrentAdjustHeight
end

---設定 UI 邊界
function UIMgr.SetUIBound(iUI)
	if iUI.m_UIBoundaryTarget == nil or iUI.m_UIBoundaryTarget.RectTransform == nil then
		return
	end

	local _MainPanelRectTransform = iUI.m_UIBoundaryTarget.RectTransform
	if _MainPanelRectTransform ~= nil then
		--設定 UI 的 Pivot 目前設定：中上
		--local _Pivot = Vector2.New(0.5,1)
		_MainPanelRectTransform.pivot = GetPivot()

		--內縮距離(寬)控制用
		local _HorizontalIndentationDistance = 0
		--內縮距離(高)控制用
		local _VerticalIndentationDistance = 0

		if this.m_IsUseBoundaryCtrl then
			_HorizontalIndentationDistance = this.m_UIIndentationDistance.Horizontal
			_VerticalIndentationDistance = this.m_UIIndentationDistance.Vertical
		end

		local _CurrentResolution = SettingMgr.GetScreenResolution()

		this.m_CanvasScaler.referenceResolution = Vector2.New(_CurrentResolution.x,_CurrentResolution.y)

		---調整後的高
		local _CurrentHeight = GetCurrentAdjustHeight(iUI)

		--取改變後 UI 的長寬
		local _UIWidth = this.m_RectTransform.rect.width - _HorizontalIndentationDistance
		local _UIHeigh = _CurrentHeight - _VerticalIndentationDistance

		--調整 UI 邊界
		if _UIWidth ~= _MainPanelRectTransform.rect.width then
			_MainPanelRectTransform:SetSizeWithCurrentAnchors(Axis.Horizontal, _UIWidth)
		end

		if _UIHeigh ~= _MainPanelRectTransform.rect.height then
			_MainPanelRectTransform:SetSizeWithCurrentAnchors(Axis.Vertical, _UIHeigh)
		end
	end
end

function UIMgr.IsDebugBoundaryCtrl()
	return this.m_IsUseBoundaryCtrl
end

---UI 開啟成功後要做甚麼 只給
function UIMgr.ToDoAfterOpenUISucceed(iUI)
	ToDoAfterOpenUISucceed(iUI)
end

--- 重刷字串 (全部重刷)
function UIMgr.ResetText()
	for key, value in pairs(_Table_UIInHierarchy) do
		value:ResetText()
	end
end

function UIMgr.LoggingOutToDo()
	_Stack_UIFullPage:Clear()
	local _TableLoggingOut_CannotClose = {UIBlackBase_Controller}
	UIMgr.CloseAll(_TableLoggingOut_CannotClose)
	UIMgr.CallAllLoggingOutToDo()
	this.Open(UIBlackBase_Controller)
	m_IsTheFirstTimeOpenMainAfterLogin = false
end

function UIMgr.DestroyAllUI()
	for key, value in pairs(_Table_UI) do
		if not table.Contains(_Table_ConnotDestroy_LoggingOut, value) then
			DestroyUI(value)
		else
			--Loading 要處理 因為他是原本就在場上
			if value == Loading_Controller then
				value.gameObject.name = value.m_UIView
			end
		end
	end
end

---取得指定的 UIOrderTable
---@param iEUIOrderLayers EUIOrderLayers 指定的UI 階層
function UIMgr.GetUIOrderTable(iEUIOrderLayers)
	return _EUIOrderTable[iEUIOrderLayers]
end

---取得指定 UI 的 sortingOrder 值
---@param iUI UIController 想要取得 sortingOrder 的 UIController
---@return number
function UIMgr.GetUIOrder(iUI)
	local _Table_UILayers = _EUIOrderTable[iUI.m_UIOrderLayerKey]
	for key, value in pairs(_Table_UILayers) do
		if value == iUI then
			if value.m_Canvas then
				return value.m_Canvas.sortingOrder
			else
				break
			end
		end
	end
	return nil
end

---刪除 UI
---@param iUI UIController 各 UI 的管理器
function UIMgr.DestroyUI(iUI)
	return DestroyUI(iUI)
end

---取得已在場上的所有 UIKey
function UIMgr.GetAllUIInHierarchyKeys()
	local _Keys = {}
	for key, value in pairs(_Table_UI) do
		table.insert(_Keys, key)
	end
	return _Keys
end

---取得 UI 實體
---@param iUI UIController 各 UI 的管理器
function UIMgr.GetUI(iUI)
	if table.Contains(_Table_UI, iUI) and this.GetUIStage(iUI) < EUIStage.Initialized then
		return _Table_UI[iUI.m_UIView]
	end
	return this.Preload(iUI, false, false)
end

function UIMgr.AddToTableNeedCloseHUDAndWalkWhenUIOpen(iUI)
	table.insert(_Table_NeedCloseHUDAndWalkWhenUIOpen, iUI)
end


---大量UI同時開啟時 相關處理code start

---檢查是否在被監控的UI行列中
---@param iCheckUI XXX_Controller 預定要開啟的UI
---@return  bool 介面Controller 是否在監測table中
------@return _HoldMethod bool  是否要保留方法且開啟介面
function UIMgr.IsInDelayyUITable(iCheckUI)
	return table.Contains(_Table_DelayUI,iCheckUI)
end

---將需要延遲的UI以及開啟方法/參數 記錄下來
---@param iUI XXX_Controller 介面的Controller
---@param iFunc function 介面的開啟方法以及開啟用的參數
function UIMgr.AddDelegateOpenUI(iUI,iFunc)

	local _Data = {}
	_Data.m_UI = iUI
	_Data.m_Open = iFunc

	table.insert(_DeletaGateFunctiobTable,_Data)

	if table.Count(_DeletaGateFunctiobTable) > 1 then
		local _CheckUIOrder = function(iUI)
			---默認給順位99 避免取不到數值排序失敗
			local _Priority = 99
			for key,value in pairs(_Table_DelayUI) do
				if value == iUI then
					_Priority = key
				end
			end
			return _Priority
		end

		table.sort(_DeletaGateFunctiobTable, function(a, b)

			local _Priority_a = _CheckUIOrder(a.m_UI)

			local _Priority_b = _CheckUIOrder(b.m_UI)

			return _Priority_a < _Priority_b
		end)
	end
end

---將排隊中的第一筆資料 移到_Cur_Opened_WaitingUI
function UIMgr.RemoveFirstDelegateUIData()
	_Cur_Opened_WaitingUI = nil
	if _DeletaGateFunctiobTable[1] ~= nil  then
		_Cur_Opened_WaitingUI = table.remove(_DeletaGateFunctiobTable,1)
	end
end

---有等待演出UI 則顯示UI 沒有則開通 _AllowFastClick_DelayUITable 允許快速提示演出
function UIMgr.TriggerDelegateOpenUI()

	UIMgr.RemoveFirstDelegateUIData()
	if _Cur_Opened_WaitingUI ~= nil  and _Cur_Opened_WaitingUI.m_Open ~= nil then
		_Cur_Opened_WaitingUI.m_Open()
	else
		UIMgr.SetAllowFastClickHint_DelayUITable(true)
	end
end

---給CommonRewardMgr用的 設定是否可以演出 快速提示
---@param iStatus bool CommonRewardMgr是否允許演出快速提示 若允許則嘗試演出
function UIMgr.SetAllowFastClickHint_CommonRewardMgrData(iStatus)
	_AllowFastClick_CommonRewardMgr = iStatus

	local _AllowFastClick , _AllowAVG = UIMgr.AllowAfterDelayTableUIDisplay()

	if _AllowFastClick then
		UIMgr.Open(FastClickHint_Controller)
	end

	if _AllowAVG then
		AVG_Controller.TriggerWaitingAVG()
	end
end

---給UIMgr用的 設定是否可以演出 快速提示
---@param iStatus bool UIMgr是否允許演出快速提示 若允許則嘗試演出
function UIMgr.SetAllowFastClickHint_DelayUITable(iStatus)
	_AllowFastClick_DelayUI = iStatus

	local _AllowFastClick , _AllowAVG = UIMgr.AllowAfterDelayTableUIDisplay()

	if _AllowFastClick then
		UIMgr.Open(FastClickHint_Controller)
	end

	if _AllowAVG then
		AVG_Controller.TriggerWaitingAVG()
	end

end

---檢查 UIMgr跟CommonRewardMgr是否同時同意演出 且 快速提示/AVG是否有等待演出訊息
---@return bool 回傳是否同意可以演出  快速提示
---@return bool 回傳是否同意可以演出  AVG
function UIMgr.AllowAfterDelayTableUIDisplay()

	local _AllowFastClickHint = false
	local _AllowFastAVG = false

	if FastClickHint_Controller == nil then
		_AllowFastClickHint =  false
	else
		local _WaitingHintAmount = FastClickHint_Controller.HasWaitingFastClickHint()
		_AllowFastClickHint =( _AllowFastClick_CommonRewardMgr and _AllowFastClick_DelayUI and (_WaitingHintAmount>0)  )
	end

	if AVG_Controller == nil  then
		_AllowFastAVG = false
	else
		local _WaitingAVGAmount = AVG_Controller.HasWaitingAVG()
		_AllowFastAVG =( _AllowFastClick_CommonRewardMgr and _AllowFastClick_DelayUI and (_WaitingAVGAmount>0)  )
	end

	return _AllowFastClickHint,_AllowFastAVG
end

---檢查用_AllowFastClick_DelayUI
---註解掉保留
--[[
function UIMgr.CheckUIType(iUI,iString)

	if iString == nil then
		iString = " nil"
	end

	if iUI == Teach_Controller  then
		return "Teach_Controller " .. iString
	elseif iUI == CommonReward_MissionComplete_Controller  then
		return "CommonReward_MissionComplete_Controller " .. iString
	elseif iUI == CommonReward_LevelUpgrade_Controller  then
		return "CommonReward_LevelUpgrade_Controller " .. iString
	elseif iUI == SelectList_Controller  then
		return "SelectList_Controller " .. iString
	elseif iUI == PopItemChange_Controller  then
		return "PopItemChange_Controller " .. iString
	elseif iUI == EventTalk_Controller  then
		return "EventTalk_Controller " .. iString
	else
		return "Other Condition"
	end
end
]]
--大量UI同時開啟時 相關處理code  end

---結束Lua時釋放Destroy UI
function UIMgr.QuitLua()
	--- Preload 的 UI 也要關閉
	for _Index, _UI in pairs(_Table_UIInHierarchy) do
		local _IsQuit = true
		---檢查是否有在不能關閉的例外清單內
		for _QuitIndex, _NotQuit in pairs(_Table_ConnotDestroy_QuitLua) do
			if _UI == _NotQuit then
				_IsQuit = false
			end
		end
		if _IsQuit then
			DestroyUI(_UI)
		end
	end
end
return UIMgr
