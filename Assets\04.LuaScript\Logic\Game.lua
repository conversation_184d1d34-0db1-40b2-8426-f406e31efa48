---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2021 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---遊戲主邏輯
---包含所有 logic Update
---@class 遊戲主邏輯
---author KK
---version 1.0
---since [ProjectBase] 0.1
---date 2021.10.12
-------------------------------------------------------------
Game = {}
local this = Game

this.m_IsLuaGameInited = false

this.m_Table_Mgrs = {}


---載入全域類型 & 全域變數
require("Common/GDefine")
require("Common/GData")
require("Common/GFunction")
require("Common/GString")
require("Common/GValue")
require("Common/ArtDefine")

---載入邏輯相關
this.m_Table_Mgrs["Logic/Data/DataMgr"] = require("Logic/Data/DataMgr")
this.m_Table_Mgrs["Logic/UIMgr"] = require("Logic/UIMgr")
this.m_Table_Mgrs["Logic/NetworkMgr"] = require("Logic/NetworkMgr")
this.m_Table_Mgrs["Logic/ProtocolMgr"] = require("Logic/ProtocolMgr")
this.m_Table_Mgrs["Logic/HEMTimeMgr"] = require("Logic/HEMTimeMgr")
this.m_Table_Mgrs["Logic/CameraMgr"] = require("Logic/CameraMgr")
this.m_Table_Mgrs["Logic/HotKey/HotKeyMgr"] = require("Logic/HotKey/HotKeyMgr")
this.m_Table_Mgrs["Logic/RoleMgr"] = require("Logic/RoleMgr")
this.m_Table_Mgrs["Logic/SceneMgr"] = require("Logic/SceneMgr")
this.m_Table_Mgrs["Logic/Battle/BattleMgr"] = require("Logic/Battle/BattleMgr")
this.m_Table_Mgrs["Logic/Battle/AutoBattleMgr"] = require("Logic/Battle/AutoBattleMgr")
this.m_Table_Mgrs["Logic/Battle/AlertRangeMgr"] = require("Logic/Battle/AlertRangeMgr")
this.m_Table_Mgrs["Logic/EventMgr"] = require("Logic/EventMgr")
this.m_Table_Mgrs["Logic/NPCMgr"] = require("Logic/NPCMgr")
this.m_Table_Mgrs["Logic/PetMgr"] = require("Logic/PetMgr")
this.m_Table_Mgrs["Logic/SelectMgr"] = require("Logic/SelectMgr")
this.m_Table_Mgrs["Logic/GearMgr"] = require("Logic/GearMgr")
this.m_Table_Mgrs["Logic/Effect/EffectMgr"] = require("Logic/Effect/EffectMgr")
this.m_Table_Mgrs["Logic/DropMgr"] = require("Logic/DropMgr")
this.m_Table_Mgrs["Logic/Icon/IconMgr"] = require("Logic/Icon/IconMgr")
this.m_Table_Mgrs["Logic/BagMgr"] = require("Logic/BagMgr")
this.m_Table_Mgrs["Logic/SearchMgr"] = require("Logic/SearchMgr")
this.m_Table_Mgrs["Logic/ClientSaveMgr"] = require("Logic/ClientSaveMgr")
this.m_Table_Mgrs["Logic/DownloadFileMgr"] = require("Logic/DownloadFileMgr")
this.m_Table_Mgrs["Logic/Map/MapMgr"] = require("Logic/Map/MapMgr")
this.m_Table_Mgrs["Logic/CDMgr"] = require("Logic/CDMgr")
this.m_Table_Mgrs["Logic/AudioMgr"] = require("Logic/AudioMgr")
this.m_Table_Mgrs["Logic/SettingMgr"] = require("Logic/SettingMgr")
this.m_Table_Mgrs["Logic/TeachMgr"] = require("Logic/TeachMgr")
this.m_Table_Mgrs["Logic/Battle/BuffMgr"] = require("Logic/Battle/BuffMgr")
this.m_Table_Mgrs["Logic/ChatMgr"] = require("Logic/ChatMgr")
this.m_Table_Mgrs["Logic/GhostMgr"] = require("Logic/GhostMgr")
this.m_Table_Mgrs["Logic/AppearanceMgr"] = require("Logic/AppearanceMgr")
this.m_Table_Mgrs["Logic/PopItemChange_Mgr"] = require("Logic/PopItemChange_Mgr")
this.m_Table_Mgrs["Logic/MessageMgr"] = require("Logic/MessageMgr")
this.m_Table_Mgrs["Logic/FilterMgr"] = require("Logic/FilterMgr")
this.m_Table_Mgrs["Logic/SellMgr"] = require("Logic/SellMgr")
this.m_Table_Mgrs["Logic/PlaySkillActMgr"] = require("Logic/PlaySkillActMgr")
this.m_Table_Mgrs["Logic/TimeMachineMgr"] = require("Logic/TimeMachineMgr")
this.m_Table_Mgrs["Controller/DragController"] = require("Controller/DragController")
this.m_Table_Mgrs["Logic/CommonQueryMgr"] = require("Logic/CommonQueryMgr")
this.m_Table_Mgrs["Logic/CommonRewardMgr"] = require("Logic/CommonRewardMgr")
this.m_Table_Mgrs["Logic/ExploreDairyMgr"] = require("Logic/ExploreDairyMgr")
this.m_Table_Mgrs["DesignPattern/GStateObserverManager"] = require("DesignPattern/GStateObserverManager")
this.m_Table_Mgrs["Logic/SystemSettingData/SpecialAffectSetting"] = require("Logic/SystemSettingData/SpecialAffectSetting")
this.m_Table_Mgrs["Logic/EnergyMineMgr"] = require("Logic/EnergyMineMgr")
this.m_Table_Mgrs["Logic/TMPMgr"] = require("Logic/TMPMgr")
this.m_Table_Mgrs["Logic/RecruitMgr"] = require("Logic/RecruitMgr")
this.m_Table_Mgrs["Logic/RedDot/RedDotMgr"] = require("Logic/RedDot/RedDotMgr")
this.m_Table_Mgrs["Logic/InputMgr"] = require("Logic/InputMgr")
this.m_Table_Mgrs["Logic/EmojiMgr"] = require("Logic/EmojiMgr")
this.m_Table_Mgrs["Logic/UnityIAP/UnityIAP"] = require("Logic/UnityIAP/UnityIAP")
this.m_Table_Mgrs["Logic/CashFlow/PurchaseMgr"] = require("Logic/CashFlow/PurchaseMgr")

if ProjectMgr.IsEditor() then
	if jit then
		D.Log("LuaJIT Version: " .. jit.version)
	else
		D.Log("Lua Version: " .. _VERSION)
	end
end

---2023.12.19 Add by KK
---請跟 C# 內 BuildData_Default.m_Bundle_Version_Code 同步, ***已經拆耦合了***
---不要再共用同個變數
---@type number 當前Bundle版本號
this.m_Bundle_Version_Code = 2
---Function
function Game.Init( iCallback_Finish )
	---下載檔案初始化
	DownloadFileMgr.Init()

	---優先取得ClientSaveMgr資料 因為需要裡面紀錄的語言相關資訊
	ClientSaveMgr.Init()
	---用是否已經簽過合約作為第一次開起遊戲的判斷
	local _ShowPanel =  not ClientSaveMgr.GetDataValue(EClientSaveDataType.Device,"m_IsAgreementPermitted")

	if _ShowPanel == true then
		Game.ShowSelectLanguagePanel(iCallback_Finish)
	else
		Game.InitPostLanguageSelect(iCallback_Finish)
	end
end

---顯示語言選擇面板
function Game.ShowSelectLanguagePanel(iCallback_Finish)

	---呼叫Unity的CommonQuery
	local _TempUI = {}
	_TempUI.gameObject = GameObject.FindGameObjectWithTag("UIMgr")
	_TempUI.m_Transform_Peak = _TempUI.gameObject.Find("Peak").transform
	local _CurrentGameObject = _TempUI.m_Transform_Peak:GetComponent("Transform"):Find("ExeCommonQuery_View").gameObject
	local _UICommonQuery = _CurrentGameObject:GetComponent("UICommonQuery")

	local function OnComfirmClick( iBtnType)
		ClientSaveMgr.ChangeDataValue(EClientSaveDataType.Device,"m_LastLanguage", (_UICommonQuery:GetDropdownValue() ))
		Game.InitPostLanguageSelect(iCallback_Finish)
	end

	_UICommonQuery:SetLanguageSelectFromLua(OnComfirmClick,true)

	_CurrentGameObject:SetActive(true)
end

---初始化 DataMgr 的協程
local _Thread_DataMgrInit = nil

local function DoAfterDataMgrInit(iErrorMsg)
	local _IsLoadDataSuccess = iErrorMsg == nil
	--串檔初始化成功後要做甚麼
	if _IsLoadDataSuccess then
		--與串檔初始話完成相關的系統可以放這
		HUDController.Init()
	else
		D.LogError("[DataMgr] 初始化失敗!!")
	end
	Game.StopReadAllData(false)
end

---初始化 DataMgr 的協程
local function DataMgrInitcoro()
	---串檔初始化
	DataMgr.Init(DoAfterDataMgrInit)
end

---停止串檔初始化動作
---@param iIsNeedResetData boolean 是否需要重置 DataMgr 的讀取資料
function Game.StopReadAllData(iIsNeedResetData)
	if iIsNeedResetData == nil then
		iIsNeedResetData = false
	end
	DataMgr.StopReadAllData(iIsNeedResetData)
	---停止讀取所有串檔
	if _Thread_DataMgrInit then
		coroutine.stop(_Thread_DataMgrInit)
		_Thread_DataMgrInit = nil
	end
end

---選語言後的 後半段初始化流程
function Game.InitPostLanguageSelect(iCallback_Finish)
	---會因為語言切換的串表 須根據當前語言選擇串表要參考的檔案
	DataMgr.ReassignedFileAccordingToLanguage()
	
	_Thread_DataMgrInit = coroutine.start(DataMgrInitcoro)

	---系統初始化
	TMPMgr.Init()
	SettingMgr.Init()
	SelectMgr.Init()
	RoleMgr.Init()
	SceneMgr.Init()
	NPCMgr.Init()
	PetMgr.Init()
	GearMgr.Init()
	EffectMgr.Init()
	DropMgr.Init()
	HEMTimeMgr.Initialize()
	BattleMgr.Init()
	AutoBattleMgr.Init()
	CDMgr.Init()
	MapMgr.Init()
	TeachMgr.Init()
	BuffMgr.Init()
	AppearanceMgr.Init()
	PopItemChange_Mgr.Init()
	MessageMgr.Init()
	CommonQueryMgr.Init()

	RecruitMgr.Init()
	ExploreDairyMgr.Init()
	---獨體初始化
	CinemachineMgr.Inst:Do()

	IconMgr.Init()
	RedDotMgr.Init()
	EmojiMgr.Init()

	AudioMgr.Init(
		function()
			---UI最後初始化
			UIMgr.Init()
			Button.InitButtonDelegateMgr()
			SpaceShipGame_Controller.AfterLuaGameInit()

			coroutine.start(function ()
				while not TMPMgr.m_IsTMPStyleSheetInited do
					coroutine.wait(0.1)
				end

				UIMgr.OpenLoading(ELoadingOpenType.Defult, Login_Controller,"","",true)
				UIMgr.Open(UIBlackBase_Controller)

				--Loading_Controller.SetDebugInfo( GameTools.ResTextGetter.GetResText( ELanguageType.TraditionalChinese, "GameInit" ))

				if iCallback_Finish then
					iCallback_Finish:Invoke()
				end
			end)
		end
	)
	InputMgr.Init()
	this.m_IsLuaGameInited = true
end

function Game.OpenLoginUI()
	UIMgr.Open(Login_Controller)
	if not ProjectMgr.IsRelease() then
		UIMgr.Open(Debug_Controller)
	end
	--開完後刷新 Login 的 Order
	coroutine.start(function()
		if not Game.m_IsLuaGameInited then
			coroutine.stop()
			return
		end
		coroutine.wait(0.5)
		UIMgr.UpdateUIOrderByUIController(Login_Controller)
	end)
end

---退出 Lua 時要做甚麼
function Game.QuitLua()
	Game.m_IsLuaGameInited = false
	Game.StopReadAllData(true)
	UIMgr.Close(Login_Controller)
	UIMgr.QuitLua()
	Game.LoggingOutToDo()
	NetworkMgr.QuitLua()
	Game.UnrequireAllMgr()
	Game.m_Table_Mgrs = {}
	GFunction.Unrequire("Game")
	collectgarbage()
end

---Unrequire 所有 Mgr
function Game.UnrequireAllMgr()
	for key, value in pairs(this.m_Table_Mgrs) do
		if value and type(value.OnUnrequire) == "function" then
			value:OnUnrequire()
		end
		GFunction.Unrequire(key)
	end
end

function Game.Update()
	if not this.m_IsLuaGameInited then
		return
	end
	---D.Log( "toLua game updateing..." );
	InputMgr.Update()
	UIMgr.Update()
	RoleMgr.Update()
	NPCMgr.Update()
	PetMgr.Update()
	SceneMgr.Update()
	MapMgr.Update()
	DataMgr.Update()
	SelectMgr.Update()
	HEMTimeMgr.Update()
	HotKeyMgr.Update()
	CDMgr.Update()
	EffectMgr.Update()
	AudioMgr.Update()
	BubbleMgr.Update()
	PopItemChange_Mgr.Update()
	MessageMgr.Update()
	DropMgr.Update()
	ExploreDairyMgr.Update()
	---2022.07.28 Add by KK Test Code
	SearchMgr.Update()

	AppearanceMgr.Update()

	BuffMgr.Update()

	MissionMgr.Update()
end

---主要處理邏輯 Update
function Game.FixedUpdate()
	if not this.m_IsLuaGameInited then
		return
	end
	UIMgr.FixedUpdate()
	HEMTimeMgr.FixedUpdate()
end

function Game.LateUpdate()
	if not this.m_IsLuaGameInited then
		return
	end
	RoleMgr.LateUpdate()
	NPCMgr.LateUpdate()
	PetMgr.LateUpdate()
	HEMTimeMgr.LateUpdate()
	CameraMgr.LateUpdate()
	ChatMgr.LateUpdate()

	SettingMgr.LateUpdate()
end

function Game.OnApplicationFocus(iIsFocus)
	if not this.m_IsLuaGameInited then
		return
	end
end

function Game.OnApplicationPause(iIsPause)
	if not this.m_IsLuaGameInited then
		return
	end
end

function Game.OnApplicationQuit()
	if this.m_IsLuaGameInited then
		SearchMgr.Destroy()
	end
	CullingGroupMgr.Inst:Dispose()
end

---登入成功後要做甚麼
function Game.AfterSuccessfulLogin(iPacket)
	Login_Model.m_IsLogin = true
	UIMgr.Open(UIOrderTop_Controller)
	local _LoginPlayerData = PlayerData.InitLoginPlayerData(iPacket)
	local _CreateRoleData = RoleCreateData:NewPlayer(_LoginPlayerData)
	-- _CreateRoleData.m_CallBack = function(iRC)
	-- 	CameraMgr.InitCinemachine()
	-- 	CameraMgr.SetMainTarget(iRC.transform)
	-- 	--2 = ECMIndex.PlayerEffectCamera
	-- 	CameraMgr.SetParent(2, iRC.m_ModelObject.transform)
	-- end
	UIMgr.Preload(Mail_Controller)
	sReceiptInfo.SetPlayerReciptInfo(131,_CreateRoleData.m_RoleID,Login_Model.GetPlayerAccount(),Login_Model.GetServerData())
	HotKeyMgr.Init()
	RoleMgr.CreatePlayer(PlayerData.GetRoleID(), _CreateRoleData)
	PlayerData.Get(EPalyerData.Flags).SetNotify("StaticFlag_Init", function()
		-- Receive_003_003_Notify()
		-- 每次登入都檢查,是否有排行榜紅點 日清永標 稱號
	end)

	MissionMgr.InitAllMission()
	AppearanceMgr.CreateDefault()
	AlertRangeMgr:Init()
	ClientSaveMgr.LoadClientFileAfterLogin()
	PurchaseMgr.Init()
	--還需要初始化最愛道具
	ItemHint_Model.SetLoveItemInit()
	TimeMachineMgr.Init()

	ChatMgr.Init()

	AutoBattleMgr.Login()

	---生成稱號權限列表
	PlayerData_Title.BuildTitleTable()
	---武裝升級初始化
	EnergyMineMgr.Init()

	SceneMgr.LoginSet()

	-- 生成 HintMgr 物件
	UIMgr.Open(HintMgr_Controller)

	--TODO: 這條先放上面給金手指用，要刪
	PetMgr.LoginSet()

	if _LoginPlayerData.m_SummonedPetPlot > 0 then
		--PetMgr.LoginSet()
		PetMgr.SetLoginPetPlot(_LoginPlayerData.m_SummonedPetPlot)

	end

	SendProtocol_016._001(1)

end

---登入後 第一次開啟主介面(過場成功)完成要做甚麼
function Game.ToDoWhenFirstTimeOpenMainAfterLogin()
	TimeMachineMgr.NotifyControl()
	ExploreDairyMgr.SetAchievementNotify()
end

---登出要做甚麼 都放在這唷喔喔
function Game.LoggingOutToDo()
	if ProjectMgr.IsDebug then
		GoldFinger_Controller.LoggingOutToDo()
	end
	SceneMgr.LogOutReset()
	BuffMgr.ClearAllData()
	MissionMgr.ResetAllMissionStep()
	AppearanceMgr.ReleaseAll(true)
	RoleMgr.LoggingOutToDo()
	SearchMgr.ClearSearchTarget()
	CullingGroupMgr.Inst:Dispose()
	BattleMgr.LogOutReset()
	BagMgr.LogOutClean()
	PlayerData.LogOutClean()
	ExploreDairyMgr.ClearAchievementNotify()
	PlayerData_Flags.LogOutClean()
	ChatMgr.LoggingOutToDo()
	HotKeyMgr.LogOutClear()
	EnergyMineMgr.LogOutClean()
	AutoBattleMgr.LogOutClean()
	TimeMachineMgr.LoggingOutToDo()
	PetMgr.LoggingOutToDo()
	MessageMgr.LoggingOutToDo()
	--=====請加在上方====
	ClientSaveMgr.LoggingOutToDo()
	UIMgr.LoggingOutToDo()

	---如果是檔案檢查會退回去 Game.m_IsLuaGameInited = false
	if Game.m_IsLuaGameInited then
		---根據是因為語言切換的登出 做相關流程的處理
		if Setting_Controller.m_IsLogingOutChangeLanguage == true then
			Game.LoggingOutLastStepFromLanguage()
		else
			Game.LoggingOutLastStep()
		end
	end

	CameraMgr.LoggingOutToDo()
end

---換日後 第一次接收到時間更新時要做甚麼
function Game.ToDoFirstReceiptTimeAfterDateChange()

end

---因為切換語言所以必須登出時 需要特別處理某些data/UI
function Game.ReInitialDateFromLanguageChange()

	---重新設定 TMP Style 以及 material
	local _ELanguage = ClientSaveMgr.GetDataValue(EClientSaveDataType.Device,"m_LastLanguage")
	TMPMgr.ChangeLanguage(_ELanguage)

	---會因為語言切換的串表 須根據當前語言選擇串表要參考的檔案
	DataMgr.ReassignedFileAccordingToLanguage()

	---一般串檔table資料設成未讀取狀態
	for key, value in pairs(DataModel.m_ChangeLanguageRnewTable) do
		value.m_IsDataInitialized = false
		value.m_DataCount = 0
		value.m_NewDataCount = 0
		value.m_StreamReadByteTimes = 0
	end

	---事件串檔table資料設成未讀取狀態
	for key, value in pairs(EventDataModel.m_ChangeLanguageRenewTable) do
		value.m_IsDataInitialized = false
		value.m_DataCount = 0
		value.m_NewDataCount = 0
		value.m_StreamReadByteTimes = 0
	end

	---一般串檔table資料開始初始化
	for key, value in pairs(DataModel.m_ChangeLanguageRnewTable) do
		value.Init()
	end
	---事件串檔table資料開始初始化
	for key, value in pairs(EventDataModel.m_ChangeLanguageRenewTable) do
		value.Init()
	end

end

---切換語言的登出 需要處理的項目
function Game.LoggingOutLastStepFromLanguage()

	---換語言要重新登入bool 設成true
	Login_Model.m_ReLogin_Language = true
	--UIMgr.LoggingOutToDo()
	UIMgr.DestroyAllUI()
	Game.ReInitialDateFromLanguageChange()
	UIMgr.OpenLoading(ELoadingOpenType.Defult, Login_Controller,"","",true)
	---設定換語言切換專屬的檔案讀取檢查
	DataMgr.SetProcessFunc()
	coroutine.start(
		function()
				---還沒讀完就先卡住
				while DataMgr.CheckIsLanguageRelativeDataInitFinish() == false do
					coroutine.wait(1)
				end
				Game.LoggingOutLastStep()
				coroutine.stop()
			end)
end

---登出最後一個步驟 開啟登入介面 關閉相關bool
function Game.LoggingOutLastStep()
	Game.OpenLoginUI()
	Login_Model.m_IsLogin = false
	Setting_Controller.m_IsLogingOut = false
	Setting_Controller.m_IsLogingOutChangeLanguage = false
end
