---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not 
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------    
---
---=====================================================================

---@class Loading_Controller 過場 Loading 介面
---<AUTHOR>
---@version 1.0
---@since [黃易群俠傳M] 0.50
---@date 2022.7.19
Loading_Controller = {}
local this = Loading_Controller
setmetatable( this, { __index = UIControllerBase } )
this:New("Loading_View", "Loading_Controller", EUIOrderLayers.Peak, true)

this.m_IsGetPicture = false

local _RotationValue = 0

local _RotationSpeed = 200

---Loding 時的背景音樂ID
LOADING_BGMID = 1

---需要被 Random 的 Index
SCENE_RANDOM_INDEX = 99

---Random 開始的 Index
SCENE_PIC_RANDOM_START = 51

---Random 結束的 Index
SCENE_PIC_RANDOM_END = 56

---
TIME_MACHINE_CRASH_TEXTURENAME = "Loading26"

SLIDERPOINT_MASK_NAME = "Slider_Bg025"

---揚州成 SceneID
SCENE_YANGZHOU_CITY_ID = 1002

---初次進入揚州成的永標
FIRST_IN_YANGZHOU_CITY_FLAG_ID = 2078

---場景描述的位置
DescriptionPosition = Vector2(0,195)

SLIDERVALUE_MAX = 36
SLIDERVALUE_QUARTER = 9

---Slider Fill 初始旋轉值
ORIGINAL_SLIDERFILL_ROTATE = Vector3(0,0,0)
---Slider 亮點 初始旋轉值
ORIGINAL_SLIDERPOINT_ROTATE = Vector3(0,0,100)
---Slider 亮點 最大旋轉值
ORIGINAL_SLIDERPOINT_ROTATE_MAX = Vector3(0,0,0)
---Slider 亮點遮罩 初始旋轉值
ORIGINAL_SLIDERPOINTPARENT_ROTATE = Vector3(0,0,-90)
---Slider 亮點遮罩 最大旋轉值
ORIGINAL_SLIDERPOINTPARENT_ROTATE_MAX = Vector3(0,0,-360)

TMP_AlphaTag_255 = "<alpha=#FF>"

TMP_AlphaTag_0 = "<alpha=#00>"

---標題的 TMPStyle
---@type string
TMPStyle_Title = "TitleBig_WBL"
---內容的 TMPStyle
---@type string
TMPStyle_Description = "TitleBig_WO_01"
---小訣竅的 TMPStyle
---@type string
TMPStyle_Detail= "W"
---跑條 TMPStyle
---@type string
TMPStyle_Slider = "BBL"

---標題的 TMPStyle
---@type string
TMPStyle_Debug = "R_01"

---目前的圖片名稱
local _CurrentTextureName = ""

--region Local function

---設定版本資訊
local function SetGameVersion()
    this.m_TMP_Version.text = GString.Format(GameTools.ResTextGetter.GetResText("Version"), Application.version, ResourceMgr.RUNTIME_RESOURCE_VERSION)
end

---初始化 TMPStyle
local function InitTMPStyle()
    this.m_Text_Title.textStyle = TMPMgr.GetTMPStyle(TMPStyle_Title)
    this.m_Text_Description.textStyle = TMPMgr.GetTMPStyle(TMPStyle_Description)
    this.m_Text_Detail.textStyle = TMPMgr.GetTMPStyle(TMPStyle_Detail)
    this.m_Text_DebugInfo.textStyle = TMPMgr.GetTMPStyle(TMPStyle_Debug)
    this.m_Text_SliderInfo.textStyle = TMPMgr.GetTMPStyle(TMPStyle_Slider)
end

---設定圖片的 Texture
local function SetPictureTexture(iPictureName, iTexture)
    --注意 圖片預設是黑色 所以要顯示圖片的時候 成為白色才會顯示圖片
    if iTexture ~= nil then
        _CurrentTextureName = iPictureName
        this.m_RawImage_BackGround.color = Color.White
        this.m_RawImage_BackGround.texture = iTexture
        this.m_IsGetPicture = true
        UIMgr.Open(Loading_Controller)
    end
end

---設定背景圖片
---@param iPictureName string 要取得的圖片名稱
---@param iLoadFromResources boolean 是否改為通過ResourceMgr來載入圖片
local function SetPicture(iPictureName, iLoadFromResources)
    if iLoadFromResources then
        SetPictureTexture(iPictureName, ResourceMgr.LoadFromResources("Texture\\" .. iPictureName))
    else
        TextureMgr.Load(iPictureName, true, function(iTex)
            SetPictureTexture(iPictureName, iTex)
        end)
    end
end

---設定 Title
---@param iTitle string 要顯示的 Title 字串
local function SetTitle(iTitle)
    this.m_Text_Title.text = ""
    local _SceneID = SceneMgr.GetSceneID()
    if string.IsNullOrEmpty(iTitle) and not (_SceneID == 0 or _SceneID == nil)then
        iTitle = TextData.Get(SceneAttributeData.Get(_SceneID).m_SceneNameID)
    end
    local _CurrentStringTitle = not string.IsNullOrEmpty(iTitle) and "<rotate=90>" .. iTitle .."</rotate>" or ""
    --設定字串
    this.m_Text_Title.text = TMP_AlphaTag_255 .. _CurrentStringTitle
end

---設定 Description
---@param iDescription string 要顯示的 Description 字串
---@param iIsNeedTMP_AlphaTag_0 boolean 是否要用 TMP_AlphaTag_0
local function SetDescription(iDescription, iIsNeedTMP_AlphaTag_0)
    --先變成空字串
    this.m_Text_Description.text = ""
    local _SceneID = SceneMgr.GetSceneID()
    if string.IsNullOrEmpty(iDescription) and not (_SceneID == 0 or _SceneID == nil) then
        iDescription = TextData.Get(SceneAttributeData.Get(_SceneID).m_SceneDescribe)
    end
    local _CurrentStringDetail = not string.IsNullOrEmpty(iDescription) and TMP_AlphaTag_255 .. "<rotate=90>".. iDescription .."</rotate>" or ""
    this.m_Text_Description.text = iIsNeedTMP_AlphaTag_0 and TMP_AlphaTag_0 .. _CurrentStringDetail or _CurrentStringDetail
end

---設定下方小提示
---@param iDetail string 要顯示的 Detail 字串
---@param iIsNeedTMP_AlphaTag_0 boolean 是否要用 TMP_AlphaTag_0
local function SetDetail(iDetail, iIsNeedTMP_AlphaTag_0)
    --先變成空字串
    this.m_Text_Detail.text = ""
    local _CurrentStringDetail = not string.IsNullOrEmpty(iDetail) and TMP_AlphaTag_255 .. iDetail or ""
    --設定字串
    this.m_Text_Detail.text = iIsNeedTMP_AlphaTag_0 and TMP_AlphaTag_0 .. _CurrentStringDetail or _CurrentStringDetail
end

---取得圖片名稱
---@param iPictureID 要取得名稱的ID
---@return string 圖片的名稱
local function GetLoadingPicName(iPictureID)
    if iPictureID == SCENE_RANDOM_INDEX then
        iPictureID = math.floor(Random.Range(SCENE_PIC_RANDOM_START, SCENE_PIC_RANDOM_END + 1))  --math.random(SCENE_PIC_RANDOM_START, SCENE_PIC_RANDOM_END + 1)
    end
    local _StringName = string.format("%s%02d", "Loading", iPictureID)
    return _StringName
end

---設定預設 Loding 時的音樂
---@param iSoundID 要撥放音樂 的 ID
local function SetDefultLoadingSound(iSoundID)
    local _SceneID = SceneMgr.GetSceneID()
    if _SceneID == 0 or _SceneID == nil then
        return
    end
    local _SceneSoundID = SceneAttributeData.Get(_SceneID).m_SceneMusic
    if iSoundID ~= _SceneSoundID then
        -- body
    end
end

---開啟 Loading UI
---@param iTitle string 標題
---@param iDescription string 敘述內容
---@param iLoadingOpenCallBack function Loading_Controller 開啟完成後要做甚麼
local function OpenLoadingDefult(iTitle, iDescription, iLoadingOpenCallBack)
	local _Title = not string.IsNullOrEmpty(iTitle) and iTitle or ""
    local _Description = not string.IsNullOrEmpty(iDescription) and iDescription or ""
    local _Detail = TextData.Get(math.random(61300001, 61300050))
    --取得 Description 寬高 
    coroutine.start(function()
        --延遲動作 在 EndFrame 後執行
        coroutine.wait(0.001)
        SetTitle(_Title)
        SetDescription(_Description)
        SetDetail(_Detail)
        SetGameVersion()
        --強設定物件位置
        this.m_RectTransform_Description.anchoredPosition = Vector2(17.5, -52.4)
        --抓目前解析度然後強制設定其對應大小
        local _CurrentResolution = SettingMgr.GetScreenResolution()
        -- 根據比例調整
        this.m_RectTransform_Description:SetSizeWithCurrentAnchors(Axis.Vertical, _CurrentResolution.x)
        this.m_RectTransform_Description:SetSizeWithCurrentAnchors(Axis.Horizontal, _CurrentResolution.y)

        --執行 CallBack
        if iLoadingOpenCallBack ~= nil then
            iLoadingOpenCallBack()
        end
    end)
end

---設定跑條的百分比字串顯示
---@param iPercentage number 跑條的百分比
local function SetSliderPercentage(iPercentage)
	this.m_GObj_SliderInfo:SetActive(true)
	this.m_TMP_SliderPercentage.text = tostring(math.floor(iPercentage * 100)) 
end
--endregion

---初始化
function Loading_Controller.Init()
    ---TMPText Debug 流程資訊
    this.m_Text_DebugInfo =  this.m_ViewRef.m_Dic_TMPText:Get("&Text_DebugInfo")

    ---TMPText 場景標題
    this.m_Text_Title =  this.m_ViewRef.m_Dic_TMPText:Get("&Text_Title")
    ---gameobject 場景標題
    this.m_Gobj_Title =  this.m_ViewRef.m_Dic_Trans:Get("&Text_Title").gameObject
    ---RectTransform 場景標題
    this.m_RectTransform_Title = this.m_Gobj_Title:GetComponent(typeof(RectTransform))

    ---TMPText 場景描述或資訊
    this.m_Text_Description =  this.m_ViewRef.m_Dic_TMPText:Get("&Text_Description")
    ---gameobject 場景描述或資訊
    this.m_Gobj_Description =  this.m_ViewRef.m_Dic_Trans:Get("&Text_Description").gameObject
    this.m_Gobj_Description:SetActive(true)
    ---ContentFitterImmediate 場景描述或資訊
    this.m_ContentFitterImmediate_Description = this.m_Gobj_Description:GetComponent(typeof(ContentFitterImmediate))
    ---RectTransform 場景描述或資訊
    this.m_RectTransform_Description = this.m_Gobj_Description:GetComponent(typeof(RectTransform))

    ---gameobject 場景描述細項
    this.m_GObj_Detail = this.m_ViewRef.m_Dic_Trans:Get("&Text_Detail").gameObject
    this.m_GObj_Detail:SetActive(true)
    ---TMPText 場景描述細項
    this.m_Text_Detail =  this.m_ViewRef.m_Dic_TMPText:Get("&Text_Detail")
    
    ---Slider Loading 本人
    this.m_GObj_Slider = this.m_ViewRef.m_Dic_Trans:Get("&Slider_Progress").gameObject
    this.m_GObj_Slider:SetActive(false)

    ---跑條資訊
    this.m_TMP_SliderPercentage = this.m_ViewRef.m_Dic_TMPText:Get("&TMP_SliderPercentage")
    this.m_GObj_SliderInfo = this.m_ViewRef.m_Dic_Trans:Get("&TMP_SliderPercentage").gameObject
    this.m_GObj_SliderInfo:SetActive(false)
    ---跑條文字
    this.m_Text_SliderInfo =  this.m_ViewRef.m_Dic_TMPText:Get("&TMP_SliderPercentage")

    InitTMPStyle()

    ---Loading 背景本人
    this.m_RawImage_BackGround = this.m_ViewRef.m_Dic_RawImage:Get("&RawImage_BG")
    ---版本資訊
    this.m_TMP_Version = this.m_ViewRef.m_Dic_TMPText:Get("&Text_Version")

end


---設定背景圖片
function Loading_Controller.SetPictureBeforeOpen(iPictureID)
    this.m_RawImage_BackGround.color = Color.Black
    this.m_RawImage_BackGround.texture = nil
    this.m_RawImage_BackGround.gameObject:SetActive(true)

    local _SceneID = SceneMgr.GetSceneID()
    if _SceneID == 0 or _SceneID == nil then
        Loading_Controller.SetLoadingDefultInfo(ELoadingDefultType.ChangeScene)
        return
    end
    if (iPictureID == nil or iPictureID <= 0 ) and _SceneID > 0 then
        iPictureID = SceneAttributeData.Get(_SceneID).m_LoadingBG
    end

    SetPicture(GetLoadingPicName(iPictureID))
end

function Loading_Controller.SetDefultBeforeOpen(iParams)
    --Title
    this.m_String_Title = iParams[1]
    --內容
    this.m_String_Description = iParams[2]
    ---是否需要 Slider
    this.m_IsNeedSlider = iParams[3]
    --Loading 開起完成後要幹嘛
    this.m_LoadingOpenedCallBack = iParams[4]
    --圖片 ID
    Loading_Controller.SetPictureBeforeOpen(iParams[5])
end

---開啟 UI
function Loading_Controller.Open(iParams)
    if this.m_IsNeedSlider then
        this.m_GObj_Slider:SetActive(true)
        this.m_GObj_SliderInfo:SetActive(true)
    else
        this.m_GObj_Slider:SetActive(false)
        this.m_GObj_SliderInfo:SetActive(false)
    end
    
    if not this.m_IsGetPicture then
        UIMgr.OpenLoading(ELoadingOpenType.Defult, UIMgr.m_CloseLoadingWhenUIOpen, unpack(iParams, 1, table.maxn(iParams)))
    else
        OpenLoadingDefult(this.m_String_Title, this.m_String_Description, this.m_LoadingOpenedCallBack)
    end

    return true
end

---關閉 UI
function Loading_Controller.Close()
    this.SetSlider(0)
    this.m_IsNeedSlider = false
    this.m_GObj_Slider:SetActive(false)
    this.m_GObj_SliderInfo:SetActive(false)
    this.SetDebugInfo("")
    this.m_IsGetPicture = false
    this.m_String_Title = ""
    this.m_String_Description = ""
    this.m_CheckCloseWhenWitchUIOpen = nil
    this.m_LoadingOpenedCallBack = nil
    this.m_Text_Title.text = ""
    this.m_Text_Description.text = ""
    this.m_Text_Detail.text = ""
    TextureMgr.DestoryObject(_CurrentTextureName)
    _CurrentTextureName = ""
    return true
end

---設定DebugString
---@param iMsg string 要想顯示的 DebugMessage
function Loading_Controller.SetDebugInfo(iMsg)
    this.m_Text_DebugInfo.text = iMsg
    this.m_Text_DebugInfo.gameObject:SetActive( not string.IsNullOrEmpty(iMsg) and ProjectMgr.IsDebug() )
end

---設定 Slider 數值
---@param iPercentage number 第幾%了 輸入用 0.XX...
function Loading_Controller.SetSlider(iPercentage)
	this.m_GObj_Slider:SetActive(true)
    this.m_GObj_SliderInfo:SetActive(true)
    SetSliderPercentage(iPercentage)
end

---設定下方小提示
---@param iDetail string 要顯示的 Detail 字串
function Loading_Controller.SetDetail(iDetail)
    SetDetail(iDetail, true)
end

---設定預設 的 LoadingUI (特定的時候用,如:下載時 等......不知道場景 ID 的狀況時使用)
---@param iELoadingDefultType ELoadingDefultType Loading 的預設類型 
function Loading_Controller.SetLoadingDefultInfo(iELoadingDefultType)
    if not this.m_RawImage_BackGround.gameObject.activeInHierarchy then
        this.m_RawImage_BackGround.color = Color.Black
        this.m_RawImage_BackGround.gameObject:SetActive(true)
    end

    if iELoadingDefultType == ELoadingDefultType.Common then
        SetPicture(GetLoadingPicName(2), true)
        SetDetail(TextData.Get(1010000))
        SetTitle(TextData.Get(1000000))
        SetDefultLoadingSound(00000)
    elseif iELoadingDefultType == ELoadingDefultType.Download then
        SetPicture(GetLoadingPicName(2), true)
        SetDetail(TextData.Get(1010001))
        SetTitle(TextData.Get(1000001))
        SetDefultLoadingSound(LOADING_BGMID)
    elseif iELoadingDefultType == ELoadingDefultType.ChangeScene then
        SetPicture(GetLoadingPicName(2), true)
        SetDetail("")
        SetTitle("")
    end
end
