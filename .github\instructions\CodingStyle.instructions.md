# 程式碼撰寫規範

## C# 編碼規範

### 命名規則

#### 變數命名

1. **成員變數** 
    - 加上前綴 `m_`
    - 例：`public int m_Count_Grid;`

2. **靜態/常數變數**
    - 使用大寫
    - 靜態變數可選擇性加上前綴 `s_`
    - 例：
        ```csharp
        private const string SYMBOL_AUTO_REF = "&";
        public static ushort s_Idx_IC = 83;
        ```

3. **參數**
    - 加上前綴 `i`
    - 例：`public void Search(Transform iTrans)`

4. **區域變數**
    - 加上前綴 `_`
    - 例：`int _Random = Random.Range(0, m_List_GetAward.Count);`

5. **列舉**
    - 加上前綴 `E`
    - 例：
        ```csharp
        private enum ETypeOfTurn 
        { 
            Simple = 0, 
            Actually, 
            Surprise, 
            Max 
        }
        ```

6. **布林變數**
    - 使用 `Is`、`Can`、`Have`、`Has` 開頭
    - 例：
        ```csharp
        public bool m_CanRun;
        bool _HaveItem;
        ```

7. **集合類型**
    - 加上集合類型名稱
    - 例：
        ```csharp
        List m_List_Name;
        Dictionary<TKey, TValue> m_Dic_Name;
        ```

### 註解規範

1. 使用內建的 `///` 格式進行文件註解
2. 類別註解必須包含：
    - 目的
    - 版本
    - 作者資訊
3. 程式碼修改時需註明：
    - 修改者
    - 修改日期
4. 註解須放置於程式碼實作前

### 程式碼結構規範

1. **變數宣告**
    - 避免使用 `var` 關鍵字（不易識別型別）
    - 所有變數宣告都必須指定型別
    - 每個變數需分開宣告

2. **程式碼複雜度**
    - 迴圈最多不超過三層
    - `switch` 陳述句必須包含 `default` 情況

3. **最佳實踐**
    - 不使用魔術數字，改用常數或列舉
    - 成員變數需使用適當的保護層級

### 格式規範

1. 縮排使用 Tab（4個空格）
2. 大括號必須對齊
3. 陳述句和函式之間需要空一行

## Lua 編碼規範

### 命名規則

1. **成員變數**
    - 加上前綴 `m_` 並標示型別
    - 例：
        ```lua
        local m_Text_Level = nil;   -- Unity Text
        local m_Trans_Root = nil;   -- Unity Transform
        local m_Obj_Root = nil;     -- Unity GameObject
        ```

2. **參數**
    - 加上前綴 `i`
    - 例：`function Demo(iStr_Level)`

3. **區域變數**
    - 加上前綴 `_`
    - 例：`local _GObj = nil;`

### 註解規範

1. 使用 `---` 作為可懸停提示的註解
2. 函式文件註解：
    - 使用 `---@param` 說明參數
    - 使用 `---@return` 說明回傳值
3. 使用 `--region` 和 `--endregion` 區分程式碼區段
4. 其他情況遵循 C# 註解風格

### 重要注意事項

1. 禁止事項：
    - 嚴禁創建名為 `self` 的變數
    - 避免使用底線加大寫字母的組合（保留給 Lua 內部使用）

2. Lua 特性：
    - 陣列索引從 1 開始（語言限制）

### 程式碼結構規範

1. Unity 物件需加上型別標示
2. 迴圈最多不超過三層
3. 需詳細記錄參數和回傳值
4. 盡可能遵循 C# 的一般程式碼原則
