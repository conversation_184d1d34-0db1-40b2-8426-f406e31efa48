---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2021 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---@class DownloadFileMgr 下載檔案管理
---<AUTHOR>
---@version 1.0
---@since [HEM2.0] 0.1
---@date 2022.8.19

---DownloadFileMgr
DownloadFileMgr = {}
local this = DownloadFileMgr

---需要下載的檔案名稱(需要和下載的檔案名稱模一樣唷)
---新增需在此加入相對應名稱
EClientDownloadFileName =
{
    [1] = "ServerList.dat",
    [2] = "ServerLight.dat",
    [3] = "BulletinData.dat",
    [4] = "UserContract.dat",
    [5] = "WebsiteData.dat"
}

---目前資源版本
this.m_CurrentResourceVersion = ""

---目前正在下載的檔案名稱
this.m_CurrentDownLoadFileName = ""

---下載完成的檔案紀錄
this.m_DownloadedNames = {}

--region local function

---取得下載檔案名稱(.dat)
local function GetDownloadFileName(iIndex)
    local _String = ""
    if not this.m_CurrentResourceVersion:IsNullOrEmpty() then
        _String = _String .. this.m_CurrentResourceVersion.."/"
    end
    _String = _String .. tostring(EClientDownloadFileName[iIndex])
    return _String
end

---取得需下載的檔案數量
local function GetNeedDownloadFileCount()
    local _NeedDownloadFileCount = 0
    for key ,value in pairs (EClientDownloadFileName) do
        if value ~= nil then
            _NeedDownloadFileCount = _NeedDownloadFileCount + 1
        end
    end
    return _NeedDownloadFileCount
end

---取得須下載完成的檔案數量
local function GetDownloadedFileCount()
    local _DownloadedFileCount = 0
    for key ,value in pairs (this.m_DownloadedNames) do
        if value ~= nil then
            _DownloadedFileCount = _DownloadedFileCount + 1
        end
    end
    return _DownloadedFileCount
end

---設定 原始的 ServerList
---@param iData table 要做處裡的 Table
local function SetOriginalServerList(iData)
    for i = 1, table.Count(iData) do
        Login_Model.m_OriginalServerList[i] = {}
        Login_Model.m_OriginalServerList[i].m_ServerName = iData[i].Name
        Login_Model.m_OriginalServerList[i].m_Ip = iData[i].IP
        Login_Model.m_OriginalServerList[i].m_ID = iData[i].ID
        Login_Model.m_OriginalServerList[i].m_Port = iData[i].PORT
    end

    return table.Count(Login_Model.m_OriginalServerList) > 0
end

---賦予到對應的檔案
---需在此加入下載後相對應檔案賦予的地方
---@param iFileName string 檔案名稱
---@param iData table 存的資料
local function SetFileToTherePlace(iFileName,iData)
    local _IsThisDownLoaded = false
    local _SetDataFunction = nil
    if iFileName == EClientDownloadFileName[1] then
        _SetDataFunction = SetOriginalServerList
    end
    if iFileName == EClientDownloadFileName[2] then
        _SetDataFunction = function (iParamData)
            Login_Model.m_OriginalSeverLight = iParamData
            return Login_Model.m_OriginalSeverLight ~= nil
        end
    end
    if iFileName == EClientDownloadFileName[3] then
        _SetDataFunction = function (iParamData)
            Login_Model.m_OriginalBulletinData = iParamData
            return Login_Model.m_OriginalBulletinData ~= nil
        end
    end
    if iFileName == EClientDownloadFileName[4] then
        _SetDataFunction = function (iParamData)
            Login_Model.m_OriginalUserContract = iParamData
            return Login_Model.m_OriginalUserContract ~= nil
        end
    end

    if iFileName == EClientDownloadFileName[5] then
        _SetDataFunction = function (iParamData)
            SettingMgr.m_OriginalWebsiteData = iParamData
            return SettingMgr.m_OriginalWebsiteData ~= nil
        end
    end

    _IsThisDownLoaded = _SetDataFunction(iData)

    if _IsThisDownLoaded then
        table.insert(this.m_DownloadedNames, GetDownloadedFileCount() + 1, this.m_CurrentDownLoadFileName)
    else
        D.LogError("檔案處理失敗，檔名:"..this.m_CurrentDownLoadFileName)
    end
end

---顯示下載完成
local function LogWhenFileDownloaded(iFileName)
    local Strtable = {}
    Strtable[1]="[DownloadFileMgr]: File Downloaded "
    Strtable[2]="("..tostring(iFileName)..")"
    local HexColorTable ={}
    HexColorTable[1]="#0080FF"
    HexColorTable[2]="yellow"
    D.Log(GString.GetTextColorfulWithHex(Strtable, HexColorTable)) 
end

---下載的 CallBack
---@param iString string 下載回傳的名稱
local function DownloadCallBack(iString)
    local _Data = nil
    local _JsonText = JsonMgr.TryRemoveUtf8BOM(iString)
    _Data = JsonMgr.JsonTextToTable(_JsonText)  
    if _Data ~= nil then
        ---顯示下載完成
        LogWhenFileDownloaded(this.m_CurrentDownLoadFileName)
    end
    
    ---字串切回正確名稱
    this.m_CurrentDownLoadFileName = string.gsub(this.m_CurrentDownLoadFileName,ResourceMgr.RUNTIME_RESOURCE_VERSION.."/","")

    ---遊戲一開始需要下載的檔案才會執行
    if table.Contains(EClientDownloadFileName, this.m_CurrentDownLoadFileName) then
        ---賦予到對應的檔案
        SetFileToTherePlace(this.m_CurrentDownLoadFileName,_Data)
        if DownloadFileMgr.IsDownloadAllJsonFile() then
            LogWhenFileDownloaded("初始化檔案下載完成")
        else
            DownloadFileMgr.GetDownloadJSONFromWeb(GetDownloadFileName(GetDownloadedFileCount() + 1) )
        end
    end

    return _Data
end

--endregion

---初始化
function DownloadFileMgr.Init()
    this.m_CurrentResourceVersion = ResourceMgr.RUNTIME_RESOURCE_VERSION
    DownloadFileMgr.GetDownloadJSONFromWeb(GetDownloadFileName(GetDownloadedFileCount() + 1) )
end

---下載指定 Json 檔案
---@param iFileName string 指定的檔案名稱
---@return table 轉成 table 的 json 檔
function DownloadFileMgr.GetDownloadJSONFromWeb(iFileName)
    local _Data = nil
    if iFileName:IsNullOrEmpty() then
        D.LogError("錯誤!!!下載檔案的為名稱為空，請檢查 DownloadFileMgr.GetDownloadJSONFromWeb 的 param")
    end
    --紀錄下載名稱
    this.m_CurrentDownLoadFileName = iFileName
    --開始下載
    FTPFileDownloadMgr.DownloadJSONFromWeb(this.m_CurrentDownLoadFileName, 
    function(iString)
        _Data = DownloadCallBack(iString)
    end)
    if _Data ~= nil then
        this.m_CurrentDownLoadFileName = ""
    end
    return _Data
end

---檢查是否都有下載完成
function DownloadFileMgr.IsDownloadAllJsonFile()
    return GetDownloadedFileCount() == GetNeedDownloadFileCount()
end

return DownloadFileMgr
