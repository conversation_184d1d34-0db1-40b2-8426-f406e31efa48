//=====================================================================
//              CHINESE GAMER PROPRIETARY INFORMATION
//
// This software is supplied under the terms of a license agreement or
// nondisclosure agreement with CHINESE GAMER and may not 
// be copied or disclosed except in accordance with the terms of that
// agreement.
//
//                 Copyright © 2025 by CHINESE GAMER.
//                      All Rights Reserved.
//
//    -------------------------------------------------------------    
//
//=====================================================================

using UnityEngine;

/// <summary>
/// UIAnimationScheduler 使用範例
/// 展示在播放第一個 SequenceItem 時初始化所有成員的功能
/// <AUTHOR>
/// @telephone 2917
/// @version 1.0
/// @since 黃易群俠傳M
/// @date 2025.8.13
/// </summary>
public class UIAnimationSchedulerExample : MonoBehaviour
{
    #region Member Variables
    [Header( "動畫管理器" )]
    /// <summary>動畫序列管理器</summary>
    public UIAnimationScheduler m_AnimationScheduler;

    [Header( "測試控制" )]
    /// <summary>是否顯示詳細日誌</summary>
    public bool m_ShowDetailedLogs = true;

    /// <summary>測試播放次數</summary>
    public int m_TestPlayCount = 3;

    /// <summary>播放間隔時間</summary>
    public float m_PlayInterval = 2.0f;

    /// <summary>當前測試次數</summary>
    private int m_CurrentTestCount = 0;
    #endregion

    #region Unity Lifecycle
    /// <summary>
    /// Unity Start 事件
    /// </summary>
    private void Start()
    {
        if( m_AnimationScheduler == null )
        {
            Debug.LogError( "[UIAnimationSchedulerExample] 動畫管理器未設定" );
            return;
        }

        // 設定除錯模式
        m_AnimationScheduler.m_ShowDebugInfo = m_ShowDetailedLogs;

        // 開始測試
        StartCoroutine( TestMultiplePlaybacks() );
    }
    #endregion

    #region Private Methods
    /// <summary>
    /// 測試多次播放
    /// </summary>
    private System.Collections.IEnumerator TestMultiplePlaybacks()
    {
        Debug.Log( $"[UIAnimationSchedulerExample] 開始測試多次播放，總共 {m_TestPlayCount} 次" );
        Debug.Log( "[UIAnimationSchedulerExample] 注意：初始化會在播放第一個項目時自動執行" );

        for( int i = 0; i < m_TestPlayCount; i++ )
        {
            m_CurrentTestCount = i + 1;
            Debug.Log( $"[UIAnimationSchedulerExample] === 第 {m_CurrentTestCount} 次播放開始 ===" );

            // 驗證所有項目
            bool _IsValid = m_AnimationScheduler.ValidateAllSequenceItems();
            Debug.Log( $"[UIAnimationSchedulerExample] 項目驗證結果: {( _IsValid ? "通過" : "失敗" )}" );

            // 播放序列（初始化會在播放第一個項目時自動執行）
            m_AnimationScheduler.PlaySequence();

            // 等待播放完成
            yield return new WaitUntil( () => m_AnimationScheduler.IsStopped );

            Debug.Log( $"[UIAnimationSchedulerExample] === 第 {m_CurrentTestCount} 次播放完成 ===" );

            // 如果不是最後一次，等待間隔時間
            if( i < m_TestPlayCount - 1 )
            {
                Debug.Log( $"[UIAnimationSchedulerExample] 等待 {m_PlayInterval} 秒後進行下一次播放..." );
                yield return new WaitForSeconds( m_PlayInterval );
            }
        }

        Debug.Log( $"[UIAnimationSchedulerExample] 所有測試完成！總共播放了 {m_TestPlayCount} 次" );
        Debug.Log( "[UIAnimationSchedulerExample] 每次播放都在第一個項目時重新初始化了所有 SequenceItem" );
    }
    #endregion

    #region Public Methods
    /// <summary>
    /// 手動觸發播放測試
    /// </summary>
    [ContextMenu( "手動播放測試" )]
    public void ManualPlayTest()
    {
        if( m_AnimationScheduler == null )
        {
            Debug.LogError( "[UIAnimationSchedulerExample] 動畫管理器未設定" );
            return;
        }

        Debug.Log( "[UIAnimationSchedulerExample] 手動觸發播放測試" );
        
        // 顯示總預估時間
        float _TotalDuration = m_AnimationScheduler.GetTotalEstimatedDuration();
        Debug.Log( $"[UIAnimationSchedulerExample] 序列總預估時間: {_TotalDuration:F2} 秒" );

        // 播放序列
        m_AnimationScheduler.PlaySequence();
    }

    /// <summary>
    /// 強制重新初始化測試
    /// </summary>
    [ContextMenu( "強制重新初始化測試" )]
    public void ForceReinitializeTest()
    {
        if( m_AnimationScheduler == null )
        {
            Debug.LogError( "[UIAnimationSchedulerExample] 動畫管理器未設定" );
            return;
        }

        Debug.Log( "[UIAnimationSchedulerExample] 強制重新初始化測試" );
        
        bool _Result = m_AnimationScheduler.ForceReinitializeAllSequenceItems();
        Debug.Log( $"[UIAnimationSchedulerExample] 強制重新初始化結果: {( _Result ? "成功" : "失敗" )}" );
    }

    /// <summary>
    /// 重置所有項目測試
    /// </summary>
    [ContextMenu( "重置所有項目測試" )]
    public void ResetAllItemsTest()
    {
        if( m_AnimationScheduler == null )
        {
            Debug.LogError( "[UIAnimationSchedulerExample] 動畫管理器未設定" );
            return;
        }

        Debug.Log( "[UIAnimationSchedulerExample] 重置所有項目測試" );
        m_AnimationScheduler.ResetAllSequenceItems();
        Debug.Log( "[UIAnimationSchedulerExample] 重置完成" );
    }
    #endregion
}
