---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2024 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---@class SkillData 技能資料
---author hui
---telephone #2950
---version 1.0
---since [黃易群俠傳M] 0.91
---date 2024.1.30
SkillData = {}
SkillData.__index = SkillData

local this = SkillData

---需要等待的串檔
this.m_NeedWaitData = {TickData,TextData}

--- 此串檔是否已經初始化完成
this.m_IsDataInitialized = false

--- 串檔總數
this.m_DataCount = 0

--- 新串了多少串檔
this.m_NewDataCount = 0

---Stream讀了多少次
this.m_StreamReadByteTimes = 0

this.m_ASSET_NAME = "SkillData_C"
local m_Dic = {}

--- 目標種類
---@type ESkillTargetKind
this.ESkillTargetKind =
{
    None = 0,
    NPC = 1,
    --- 玩家
    Player = 2,
    --- 全部
    All = 3,
    --- 自訂圓心
    Appoint = 4,
}

function SkillData:New(iReader)
    ---@type SkillData
    local _Data = {}
    setmetatable ( _Data, SkillData )

    ---招式 ID
    ---@type ushort
    _Data.m_Idx = iReader:ReadUInt16()
    ---稀有度
    ---@type byte
    _Data.m_Rarity = iReader:ReadByte()
    ---招式欄位 1.普攻 2.絕學1 3.絕學2 4.絕學3 5.絕學4 6.核心技 7.翻滾/衝刺/迴避 8.? 101.內功(暫)
    ---@type byte
    _Data.m_SkillType = iReader:ReadByte()
    ---Skill 階級（不是品階）
    ---@type byte
    _Data.m_SkillClass = iReader:ReadByte()
    --- 下一階 Skill ID
    ---@type ushort
    _Data.m_NextSkillId = iReader:ReadUInt16()
    --- 最初 Skill ID
    ---@type ushort
    _Data.m_OriginalSkillId = iReader:ReadUInt16()
    ---施放類型
    ---@type byte
    _Data.m_UseType = iReader:ReadByte()
    ---充能數量
    ---@type byte
    _Data.m_PowerCount = iReader:ReadByte()
    ---招式串接
    ---@type ushort
    _Data.m_SkillOrderIdx = iReader:ReadUInt16()
    ---招式順序
    ---@type byte
    _Data.m_SkillOrder = iReader:ReadByte()
    ---集氣招式來源
    ---@type ushort
    _Data.m_OriginalSource = iReader:ReadUInt16()
    ---時間參數
    ---@type ushort
    _Data.m_TimeParm = iReader:ReadUInt16()
    ---開放等級
    ---@type byte
    _Data.m_OpenLV = iReader:ReadByte()
    ---獨立冷卻時間 ( 毫秒、公式表 )
    ---@type uint
    _Data.m_SingleCD = iReader:ReadUInt32()
    ---共通冷卻時間 ( 毫秒 )
    ---@type uint
    _Data.m_CommonCD = iReader:ReadUInt32()
    ---攻擊範圍
    ---@type ushort
    _Data.m_LimitRange = iReader:ReadUInt16()
    ---技能半徑
    ---@type ushort
    _Data.m_SkillRange = iReader:ReadUInt16()
    ---招式名稱字串編號
    ---@type uint
    _Data.m_NameId = iReader:ReadUInt32()
    ---招式名稱
    _Data.m_Name = TextData.Get(_Data.m_NameId)
    ---喊招字串
    ---@type uint
    _Data.m_CallSkillTextId = iReader:ReadUInt32()
    ---招式 icon 下方提示字串
    ---@type byte
    _Data.IconHotkeyTextId = iReader:ReadByte()
    ---武功編號(反查)
    ---@type ushort
    _Data.m_WugongID = iReader:ReadUInt16()
    ---可否移動中斷 ( 0.否 1.是 )
    ---@type bool
    _Data.m_MoveBreak = iReader:ReadBoolean()
    ---可以打斷其他技能 ( 0.否 1.是 )
    ---@type bool
    _Data.m_CanBreakOffSkill = iReader:ReadBoolean()
    ---位移類型( 0.無 1.對人位移(衝鋒) 2.對點位移(突進) 3.背刺 )
    ---@type byte
    _Data.m_MoveType = iReader:ReadByte()
    ---位移方向( 0.無 1.前方 2.後方 )
    ---@type byte
    _Data.m_MoveDir = iReader:ReadByte()
    ---位移距離
    ---@type ushort
    _Data.m_MoveDistance = iReader:ReadUInt16()
    ---動作檔ID
    ---@type ushort
    _Data.m_ActId = iReader:ReadUInt16()
    ---招式時間
    ---@type ushort
    _Data.m_SkillTime = iReader:ReadUInt16()

    _Data.m_TickAy = {}
    for i = 1, 8 do
        _Data.m_TickAy[i] = {}
        ---tick編號
        ---@type ushort
        _Data.m_TickAy[i].m_TickId = iReader:ReadUInt16()
        ---Tick時間
        ---@type ushort
        _Data.m_TickAy[i].m_TickTime = iReader:ReadUInt16()
    end

    ---Icon類型
    ---@type byte
    _Data.m_IconType = iReader:ReadByte()
    ---Icon編號
    ---@type ushort
    _Data.m_IconId = iReader:ReadUInt16()
    ---內力消耗 公式編號
    ---@type ushort
    _Data.m_SpendMpFormulaId = iReader:ReadUInt16()
    ---內力消耗 公式參數
    ---@type ushort
    _Data.m_SpendMpParameter = iReader:ReadUInt16()
    ---真氣消耗 公式編號
    ---@type ushort
    _Data.m_SpendSpFormulaId = iReader:ReadUInt16()
    ---真氣消耗 公式參數
    ---@type ushort
    _Data.m_SpendSpParameter = iReader:ReadUInt16()
    ---氣血消耗 公式編號
    ---@type ushort
    _Data.m_SpendHpFormulaId = iReader:ReadUInt16()
    ---氣血消耗 公式參數
    ---@type ushort
    _Data.m_SpendHpParameter = iReader:ReadUInt16()
    ---招式目標種類 ( 0.自己 1.Npc 2.玩家 3.全部 4.地板 )
    ---@type byte
    _Data.m_TargetKind = iReader:ReadByte()
    ---招式目標陣營 ( 1.敵方 2.隊伍 3.除了敵方 4.公會 5.全部 6.全部不含自己 )
    ---@type byte
    _Data.m_TargetGroup = iReader:ReadByte()
    ---此招式會歸類為指定目標技能 (填 1 會送協定 8-1 8-2)
    ---@type byte
    _Data.m_SendTargetProtcol = iReader:ReadByte()
    ---招式敘述字串編號
    ---@type uint
    _Data.m_InfoStrId = iReader:ReadUInt32()

    ---掛機回復公式
    ---@type ushort
    _Data.m_AutoBattleRestoreFormula = iReader:ReadUInt16()

    ---循環總數 (給循環類招式用的)
    ---@type ushort
    _Data.m_CyclesCount = iReader:ReadUInt16()

    ---判斷 BuffID
    ---@type ushort
    _Data.m_CheckBuffId = iReader:ReadUInt16()
    ---判斷 Buff 層數
    ---@type ushort
    _Data.m_CheckBuffStacks = iReader:ReadUInt16()
    ---判斷數值(用來判斷是氣血、真氣、內力、磁能)
    ---@type byte
    _Data.m_CheckUseType = iReader:ReadByte()
    ---判斷規則(用來檢查是大於或小於的情況判斷)
    ---@type byte
    _Data.m_CheckUseRule = iReader:ReadByte()
    ---%數判斷(檢查數值%數)
    ---@type byte
    _Data.m_CheckUsePercent = iReader:ReadByte()

    --- 核心技被動 buff
    _Data.CoreSkill = {}
    for i = 1, 5 do
        _Data.CoreSkill[i] = iReader:ReadUInt16()
    end

    ---演武模式 0:一隻NPC 1:只有自己 2+:幾個敵人
    ---@type ushort
    _Data.m_WugongDemoMode = iReader:ReadUInt16()
    ---演武範圍參數
    ---@type ushort
    _Data.m_WugongDemoRange = iReader:ReadUInt16()
    ---掛機技能分類
    ---@type ushort
    _Data.m_AutoSkillKind = iReader:ReadUInt16()
    ---施放後進入戰鬥狀態
    ---@type bool
    _Data.m_ToBattleState = iReader:ReadBoolean()
    ---駕駛坐騎才可使用
    ---@type byte
    _Data.m_OnDrivingLaunch = iReader:ReadByte()
    ---自帶特效編號
    ---@type ushort
    _Data.m_LaunchEffectId = iReader:ReadUInt16()
    ---自帶音效編號
    ---@type ushort
    _Data.m_LaunchSoundId = iReader:ReadUInt16()
    ---更換招式顏色 2.0 暫時無用 同格換成 技能分類欄位
    ---@type byte
    --_Data.m_ChangeSkillColor = iReader:ReadByte()
    ---技能分類欄位
    ---@type byte
    _Data.m_SkillDivide = iReader:ReadByte()
    ---套用距離屬性修正值編號
    ---@type ushort
    _Data.m_AttributesNum = iReader:ReadUInt16()

    return _Data.m_Idx, _Data
end

---初始化
function SkillData.Init()
    return DataReader.LoadFile(this.m_ASSET_NAME, SkillData.OnLoadData)
end

---讀檔
function SkillData.OnLoadData(iFile)
    local _Reader = DataReader.New(iFile)
    this.m_DataCount = _Reader:ReadUInt32()
    DataMgr.NewData(this, _Reader, m_Dic)
end

---外掛串表讀檔
function SkillData.OnLoadExTableData(iIndex, iData)
    for j = 1, table.Count(iData.m_TickAy) do
        if iData.m_TickAy[j].m_TickId ~= 0 then
            --- Tick 資料
            iData.m_TickAy[j].m_TickDataAy = TickData.GetTickDataByIdx(iData.m_TickAy[j].m_TickId)
            if iData.m_TickAy[j].m_TickDataAy == nil then
                D.LogError("SkillData _Idx: " .. iIndex .. " m_TickId: ".. iData.m_TickAy[j].m_TickId)
            end
        end
    end

    -- 塞集氣招式燈號資料
    if iData.m_UseType == EHotKeyUseKind.PowerSkill then
        -- 如果已經是最後一招
        if iData.m_SkillOrderIdx == 0 and iData.m_OriginalSource ~= 0 then
            if m_Dic[iData.m_OriginalSource] then
                --- 最大登號
                m_Dic[iData.m_OriginalSource].m_MaxLightCount = iData.m_SkillOrder

                if m_Dic[iData.m_OriginalSource].m_MaxSkillCount == nil then
                    --- 最大時間
                    m_Dic[iData.m_OriginalSource].m_MaxSkillTime = iData.m_TimeParm * 0.001
                else
                    m_Dic[iData.m_OriginalSource].m_MaxSkillTime =
                        iData.m_TimeParm * 0.001 > m_Dic[iData.m_OriginalSource].m_MaxSkillTime and iData.m_TimeParm * 0.001 or m_Dic[iData.m_OriginalSource].m_MaxSkillTime
                end
            end
        end
    elseif iData.m_UseType == EHotKeyUseKind.Loop_2 then
        if iData.m_SkillOrderIdx ~= 0 and iData.m_OriginalSource ~= 0 then
            if m_Dic[iData.m_OriginalSource] then
                if m_Dic[iData.m_OriginalSource].m_MaxSkillCount == nil then
                    --- 最大循環招式數量
                    m_Dic[iData.m_OriginalSource].m_MaxSkillCount = iData.m_SkillOrder
                else
                    m_Dic[iData.m_OriginalSource].m_MaxSkillCount =
                        iData.m_SkillOrder > m_Dic[iData.m_OriginalSource].m_MaxSkillCount and iData.m_SkillOrder or m_Dic[iData.m_OriginalSource].m_MaxSkillCount
                end
            end
        end
    end

    return iData
end

---取得SkillData
---@param m_Idx SkillDatam_Idx
---@return SkillData
function SkillData.GetSkillDataByIdx(iIdx)
    if iIdx > 0 and m_Dic[iIdx] ~= nil then
        return m_Dic[iIdx]
    else
        D.Log("Cant Find SkillData m_Idx: " .. iIdx)
        return nil
    end
end

---============分隔線以上為自動填入區段，功能請勿於此線以上撰寫================
---======================== I am Spliter! ===============================

--- 取全部表 (寫給 editor 工具用的)
function SkillData.GetAllData()
	return m_Dic
end

---@class SkillData.EMoveDirType 招式位移方向
SkillData.EMoveDirType =
{
    None = 0,
    --- 前方
    Forward = 1,
    --- 後方
    Back = 2,
}

---@class SkillData.EMoveType 招式位移種類
SkillData.EMoveType =
{
    None = 0,
    --- 對目標
    Target = 1,
    --- 對座標
    Point = 2,
    --- 背刺
    TargetPos = 3,
    --- 隨機左右 分身
    RandomPos = 4,
}

SkillData.ESkillType =
{
    --- 普功
    NormalAtk = 1,
    --- 絕學 1
    Skill_1 = 2,
    --- 絕學 2
    Skill_2 = 3,
    --- 絕學 3
    Skill_3 = 4,
    --- 絕學 4
    Skill_4 = 5,
    --- 核心技
    CoreSkill = 6,
    --- 位移
    MoveSkill = 7,
    --- 內功
    InternalSkill = 101,
}

--- 各武器普功 Skill 資料 ( 5 種武器 * 3 招 skill )
SkillData.m_NormalAtkSkillAy = {}

function SkillData.SetWugongData()
    for _Key, _Value in pairs (m_Dic) do
        if _Value.m_WugongData == nil and _Value.m_WugongID ~= 0 then
            _Value.m_WugongData = WugongData.GetWugongDataByIdx(_Value.m_WugongID)
        end
    end
end

--- 取得技能 Icon 圖片名稱
---@param iWithSkillClass boolean 需不需判斷進階技能
function SkillData:GetIconTextureName(iNeedCheckSkillClass)
    if self.m_IconId == 0 or self.m_IconType == 0 then
        if self.m_WugongData == nil then
            D.LogError("技能圖片設定有問題 ( 且沒有設定所屬武功 ): ".. self.m_Idx)
            return ""
        end
        return self.m_WugongData:GetIconTextureName()
    else
        if Login_Model.m_IsLogin and iNeedCheckSkillClass and self.m_SkillClass ~= 0 then
            local _FindClassSkill = true
            local _TempSkillData = self
            local _NextSkillData = SkillData.GetSkillDataByIdx(_TempSkillData.m_NextSkillId)
            while _NextSkillData ~= nil do
                if _TempSkillData.m_NextSkillId == 0 then
                    _FindClassSkill = false
                    break
                end

                if not PlayerData_Wugong.GetWugongData(_NextSkillData.m_WugongID) then
                    -- 沒有學到進階技能對應的武功，就把下一階的技能清掉
                    _NextSkillData = nil
                    break
                else
                    -- 取得下一階的 Skill
                    _TempSkillData = SkillData.GetSkillDataByIdx(_TempSkillData.m_NextSkillId)

                    -- 取得再下一階的 Skill
                    _NextSkillData = SkillData.GetSkillDataByIdx(_TempSkillData.m_NextSkillId)
                end
            end
            return ICON_STR .. GValue.Zero_stuffing(_TempSkillData.m_IconId + _TempSkillData.m_IconType * 10000, 6) or ""
        else
            return ICON_STR .. GValue.Zero_stuffing(self.m_IconId + self.m_IconType * 10000, 6) or ""
        end
    end
end

--- 取得武功名稱
---@param iStyle string 文字 Style (沒給則不套用)
function SkillData:SkillName(iStyle)
	local result = ""

	if DataMgr.m_IS_SHOW_IDX then
		result = self.m_Idx.."_"..self.m_Name
	else
		result = self.m_Name
	end

	if iStyle then
		result = GString.StringWithStyle(result, iStyle)
	end

	return result
end

--- 取得武功名稱
---@param iSkillID number 技能編號
---@param iStyle string 文字 Style (沒給則不套用)
function SkillData.GetSkillName(iSkillID, iStyle)
    if m_Dic[iSkillID] then
        return m_Dic[iSkillID]:SkillName(iStyle)
    else
        local _result = "找不到 SkillID: " .. tostring(iSkillID)
        D.LogError(_result)
        return _result
    end
end

function SkillData:GetSkillTime()
    return (self.m_SkillTime + BattleSetting.m_SkillEndTimeAdd) * 0.001
end

--- 設定招式資料
function SkillData.SetAtkSkillAy()
    for _Key, _Value in pairs (BattleSetting.m_NormalAtkWugong) do
        if _Value ~= 0 then
            local _TempWugong = WugongData.GetWugongDataByIdx(_Value)
            SkillData.m_NormalAtkSkillAy[_Key] = {}
            SkillData.m_NormalAtkSkillAy[_Key][1] = _TempWugong.m_SkillDataAy[1].m_SkillData
            SkillData.m_NormalAtkSkillAy[_Key][2] = _TempWugong.m_SkillDataAy[2].m_SkillData
            SkillData.m_NormalAtkSkillAy[_Key][3] = _TempWugong.m_SkillDataAy[3].m_SkillData
        end
    end
end

--- 取得普功招式
---@param iWeaponType number 武器種類
---@param iIdx number 第幾招
function SkillData.GetNormalAtkSkillData(iWeaponType, iIdx)
    return SkillData.m_NormalAtkSkillAy[table.GetKey(EWeaponType, iWeaponType)][iIdx]
end

--- 戰鬥編輯器用
function SkillData.GetAllSkillData()
    return m_Dic
end
--- 戰鬥編輯器用 抓全部有用的SkillActID
function SkillData.GetAllActID()
    local _ActIDAy = {}
    for _Key, _Value in pairs(m_Dic) do
        if _Value.m_ActId ~= 0 then
            table.insert(_ActIDAy, _Value.m_ActId)
        end
    end
    return _ActIDAy
end

--- 反查武功ID
---@param iSkillID number SkillData
---@return number WugongID
---@return boolean isSuccess
function SkillData.GetWugongDataBySkillID(iSkillID)
    local _SkillData = SkillData.GetSkillDataByIdx(iSkillID)
    if not _SkillData or not _SkillData.m_WugongData then
        return nil, false
    end

    return _SkillData.m_WugongData, true
end

function SkillData.GetChainWugongCount(iSkillID)
    -- 開關順序按鈕 Panel
    local _SkillData = SkillData.GetSkillDataByIdx(iSkillID)
    local _Number = 0

    local function _FindChain(iSkillData)

        if(iSkillData.m_SkillOrderIdx ~= 0) then
            _FindChain(SkillData.GetSkillDataByIdx(iSkillData.m_SkillOrderIdx))
            _Number = _Number + 1
        end

        return _Number

    end

    if(_SkillData.m_UseType == EHotKeyUseKind.Power) then
        return _FindChain(_SkillData)
    end

    return 0
end

--- 檢查技能是否損毀
function SkillData.CheckIsBroken(iType)
    local _IsBroken = nil
    for _Key, _Value in pairs(BattleSetting.m_BrokenData) do
        if table.Contains(_Value, iType) then
            if _IsBroken == nil then
                _IsBroken = not PlayerData_Flags[EFlags.Static].IsHaveFlag(_Key)
                return _IsBroken
            end
        end
    end
    return false
end

--- 取得技能的招式順序對應的技能編號
---@param iSkill number 技能編號
---@param iOrder number 招式順序
---@return number 技能編號
function SkillData.FindPowerSkill(iSkill, iOrder)
    local _TempSkill = SkillData.GetSkillDataByIdx(iSkill)
    if _TempSkill ~= nil then
        if _TempSkill.m_SkillOrder == iOrder then
            return _TempSkill.m_Idx
        else
            if _TempSkill.m_SkillOrderIdx == 0 then
                return 0
            end
            return SkillData.FindPowerSkill(_TempSkill.m_SkillOrderIdx, iOrder)
        end
    end
    return 0
end
