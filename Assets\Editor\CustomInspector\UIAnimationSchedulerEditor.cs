//=====================================================================
//              CHINESE GAMER PROPRIETARY INFORMATION
//
// This software is supplied under the terms of a license agreement or
// nondisclosure agreement with CHINESE GAMER and may not 
// be copied or disclosed except in accordance with the terms of that
// agreement.
//
//                 Copyright © 2025 by CHINESE GAMER.
//                      All Rights Reserved.
//
//    -------------------------------------------------------------    
//
//=====================================================================

using UnityEditor;
using UnityEngine;
using UnityEditorInternal;

/// <summary>
/// 請輸入結構描述, 用途
/// <AUTHOR>
/// @telephone 2917
/// @version 1.0
/// @since 黃易群俠傳M
/// @date 2025.8.12
/// </summary>
[CustomEditor(typeof( UIAnimationScheduler ) )]
public class UIAnimationSchedulerEditor : Editor
{
    #region Member Variables
    private SerializedProperty m_SequenceItems;
    private SerializedProperty m_IsPlayOnStart;
    private SerializedProperty m_IsLoop;
    private SerializedProperty m_LoopCount;
    private SerializedProperty m_ShowDebugInfo;
    private SerializedProperty m_OnSequenceStart;
    private SerializedProperty m_OnSequenceFinished;
    private SerializedProperty m_OnItemStart;
    private SerializedProperty m_OnItemFinished;

    private ReorderableList m_ReorderableList;
    private UIAnimationScheduler m_Target;

    private bool m_ShowPlaybackControls = true;
    private bool m_ShowSequenceSettings = true;
    private bool m_ShowEvents = false;
    private bool m_ShowDebugSettings = false;

    private GUIStyle m_HeaderStyle;
    private GUIStyle m_ButtonStyle;
    private GUIStyle m_PlayButtonStyle;
    private GUIStyle m_StopButtonStyle;
    #endregion

    #region Unity Editor Lifecycle
    /// <summary>
    /// Unity OnEnable 事件
    /// </summary>
    private void OnEnable()
    {
        m_Target = (UIAnimationScheduler)target;

        // 取得序列化屬性
        m_SequenceItems = serializedObject.FindProperty( "m_SequenceItems" );
        m_IsPlayOnStart = serializedObject.FindProperty( "m_IsPlayOnStart" );
        m_IsLoop = serializedObject.FindProperty( "m_IsLoop" );
        m_LoopCount = serializedObject.FindProperty( "m_LoopCount" );
        m_ShowDebugInfo = serializedObject.FindProperty( "m_ShowDebugInfo" );
        m_OnSequenceStart = serializedObject.FindProperty( "m_OnSequenceStart" );
        m_OnSequenceFinished = serializedObject.FindProperty( "m_OnSequenceFinished" );
        m_OnItemStart = serializedObject.FindProperty( "m_OnItemStart" );
        m_OnItemFinished = serializedObject.FindProperty( "m_OnItemFinished" );

        // 建立可重新排序的清單
        CreateReorderableList();
    }

    /// <summary>
    /// Unity OnInspectorGUI 事件
    /// </summary>
    public override void OnInspectorGUI()
    {
        serializedObject.Update();

        // 延遲初始化樣式，避免在 OnEnable 呼叫 GUI API
        if( m_HeaderStyle == null || m_ButtonStyle == null || m_PlayButtonStyle == null || m_StopButtonStyle == null )
        {
            InitializeStyles();
        }

        DrawCustomHeader();
        EditorGUILayout.Space( 10 );

        DrawPlaybackControls();
        EditorGUILayout.Space( 10 );

        DrawSequenceSettings();
        EditorGUILayout.Space( 5 );

        DrawSequenceItems();
        EditorGUILayout.Space( 5 );

        DrawEvents();
        EditorGUILayout.Space( 10 );

        DrawDebugSettings();

        serializedObject.ApplyModifiedProperties();
    }
    #endregion

    #region Private Methods
    /// <summary>
    /// 初始化樣式
    /// </summary>
    private void InitializeStyles()
    {
        if( m_HeaderStyle == null )
        {
            m_HeaderStyle = new GUIStyle( EditorStyles.boldLabel )
            {
                fontSize = 16,
                alignment = TextAnchor.MiddleCenter
            };
        }

        if( m_ButtonStyle == null )
        {
            m_ButtonStyle = new GUIStyle( GUI.skin.button )
            {
                fontSize = 12,
                fontStyle = FontStyle.Bold
            };
        }

        if( m_PlayButtonStyle == null )
        {
            m_PlayButtonStyle = new GUIStyle( m_ButtonStyle );
            m_PlayButtonStyle.normal.textColor = Color.green;
        }

        if( m_StopButtonStyle == null )
        {
            m_StopButtonStyle = new GUIStyle( m_ButtonStyle );
            m_StopButtonStyle.normal.textColor = Color.red;
        }
    }

    /// <summary>
    /// 建立可重新排序的清單
    /// </summary>
    private void CreateReorderableList()
    {
        m_ReorderableList = new ReorderableList( serializedObject, m_SequenceItems, true, true, true, true );

        // 設定標題繪製
        m_ReorderableList.drawHeaderCallback = ( Rect iRect ) =>
        {
            EditorGUI.LabelField( iRect, "序列項目清單", EditorStyles.boldLabel );
        };

        // 設定元素繪製
        m_ReorderableList.drawElementCallback = DrawSequenceItemElement;

        // 設定元素高度
        m_ReorderableList.elementHeightCallback = GetSequenceItemElementHeight;

        // 設定新增元素回調
        m_ReorderableList.onAddCallback = ( ReorderableList iList ) =>
        {
            int _Index = iList.serializedProperty.arraySize;
            iList.serializedProperty.arraySize++;
            iList.index = _Index;

            var _Element = iList.serializedProperty.GetArrayElementAtIndex( _Index );
            var _Name = _Element.FindPropertyRelative( "m_Name" );
            _Name.stringValue = $"Sequence Item {_Index + 1}";
        };
    }

    /// <summary>
    /// 繪製自訂標題
    /// </summary>
    private void DrawCustomHeader()
    {
        EditorGUILayout.LabelField( "LeanTween 序列管理器", m_HeaderStyle );

        // 顯示當前狀態
        if( Application.isPlaying && m_Target != null )
        {
            string _StatusText = $"狀態: {m_Target.CurrentState} | 索引: {m_Target.CurrentIndex}/{m_Target.ItemCount}";
            EditorGUILayout.LabelField( _StatusText, EditorStyles.centeredGreyMiniLabel );
        }
    }

    /// <summary>
    /// 繪製播放控制
    /// </summary>
    private void DrawPlaybackControls()
    {
        m_ShowPlaybackControls = EditorGUILayout.Foldout( m_ShowPlaybackControls, "播放控制", true );

        if( m_ShowPlaybackControls )
        {
            EditorGUILayout.BeginVertical( "box" );

            if( Application.isPlaying && m_Target != null )
            {
                EditorGUILayout.BeginHorizontal();

                // 播放按鈕
                GUI.enabled = !m_Target.IsPlaying;
                if( GUILayout.Button( "▶ 播放", m_PlayButtonStyle, GUILayout.Height( 30 ) ) )
                {
                    m_Target.PlaySequence();
                }

                // 停止按鈕
                GUI.enabled = !m_Target.IsStopped;
                if( GUILayout.Button( "⏹ 停止", m_StopButtonStyle, GUILayout.Height( 30 ) ) )
                {
                    m_Target.StopSequence();
                }

                // 暫停/恢復按鈕
                GUI.enabled = m_Target.IsPlaying || m_Target.IsPaused;
                string _PauseResumeText = m_Target.IsPaused ? "▶ 恢復" : "⏸ 暫停";
                if( GUILayout.Button( _PauseResumeText, m_ButtonStyle, GUILayout.Height( 30 ) ) )
                {
                    if( m_Target.IsPaused )
                        m_Target.ResumeSequence();
                    else
                        m_Target.PauseSequence();
                }

                GUI.enabled = true;
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.Space( 5 );

                // 導航控制
                EditorGUILayout.BeginHorizontal();

                GUI.enabled = m_Target.CurrentIndex > 0;
                if( GUILayout.Button( "⏮ 上一個", GUILayout.Height( 25 ) ) )
                {
                    m_Target.PlayPrevious();
                }

                GUI.enabled = m_Target.CurrentIndex < m_Target.ItemCount - 1;
                if( GUILayout.Button( "⏭ 下一個", GUILayout.Height( 25 ) ) )
                {
                    m_Target.PlayNext();
                }

                GUI.enabled = true;
                EditorGUILayout.EndHorizontal();

                // 跳轉到指定索引
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField( "跳轉到索引:", GUILayout.Width( 80 ) );
                int _JumpIndex = EditorGUILayout.IntField( m_Target.CurrentIndex, GUILayout.Width( 50 ) );
                if( GUILayout.Button( "跳轉", GUILayout.Width( 50 ) ) )
                {
                    m_Target.JumpToIndex( _JumpIndex );
                }
                EditorGUILayout.EndHorizontal();
            }
            else
            {
                EditorGUILayout.HelpBox( "播放控制僅在運行時可用", MessageType.Info );
            }

            EditorGUILayout.EndVertical();
        }
    }

    /// <summary>
    /// 繪製序列設定
    /// </summary>
    private void DrawSequenceSettings()
    {
        m_ShowSequenceSettings = EditorGUILayout.Foldout( m_ShowSequenceSettings, "序列設定", true );

        if( m_ShowSequenceSettings )
        {
            EditorGUILayout.BeginVertical( "box" );

            EditorGUILayout.PropertyField( m_IsPlayOnStart, new GUIContent( "啟動時自動播放" ) );
            EditorGUILayout.PropertyField( m_IsLoop, new GUIContent( "循環播放" ) );

            if( m_IsLoop.boolValue )
            {
                EditorGUI.indentLevel++;
                EditorGUILayout.PropertyField( m_LoopCount, new GUIContent( "循環次數 (-1 為無限)" ) );
                EditorGUI.indentLevel--;
            }

            EditorGUILayout.EndVertical();
        }
    }

    /// <summary>
    /// 繪製事件設定
    /// </summary>
    private void DrawEvents()
    {
        m_ShowEvents = EditorGUILayout.Foldout( m_ShowEvents, "事件設定", true );

        if( m_ShowEvents )
        {
            EditorGUILayout.BeginVertical( "box" );

            EditorGUILayout.PropertyField( m_OnSequenceStart, new GUIContent( "序列開始事件" ) );
            EditorGUILayout.PropertyField( m_OnSequenceFinished, new GUIContent( "序列完成事件" ) );
            EditorGUILayout.PropertyField( m_OnItemStart, new GUIContent( "項目開始事件" ) );
            EditorGUILayout.PropertyField( m_OnItemFinished, new GUIContent( "項目完成事件" ) );

            EditorGUILayout.EndVertical();
        }
    }

    /// <summary>
    /// 繪製除錯設定
    /// </summary>
    private void DrawDebugSettings()
    {
        m_ShowDebugSettings = EditorGUILayout.Foldout( m_ShowDebugSettings, "除錯設定", true );

        if( m_ShowDebugSettings )
        {
            EditorGUILayout.BeginVertical( "box" );
            EditorGUILayout.PropertyField( m_ShowDebugInfo, new GUIContent( "顯示除錯資訊" ) );
            EditorGUILayout.EndVertical();
        }
    }

    /// <summary>
    /// 取得名稱的基底（去除最後的 index 數字）
    /// </summary>
    /// <param name="iName">原始名稱</param>
    /// <returns>基底名稱</returns>
    private static string GetBaseName( string iName )
    {
        int _IdxPos = iName.LastIndexOf( ' ' );
        if( _IdxPos > 0 )
        {
            string _TryNum = iName.Substring( _IdxPos + 1 );
            if( int.TryParse( _TryNum, out _ ) )
                return iName.Substring( 0, _IdxPos );
        }
        return iName;
    }

    /// <summary>
    /// 依序自動補上 index 的名稱
    /// </summary>
    /// <param name="iElement">序列化元素</param>
    /// <param name="iIndex">索引</param>
    private static void UpdateNameWithIndex( SerializedProperty iElement, int iIndex )
    {
        var _NameProp = iElement.FindPropertyRelative( "m_Name" );
        string _BaseName = GetBaseName( _NameProp.stringValue );
        _NameProp.stringValue = _BaseName + " " + ( iIndex + 1 );
    }

    /// <summary>
    /// 重新編號所有項目名稱
    /// </summary>
    private void RefreshAllNames()
    {
        for( int i = 0; i < m_SequenceItems.arraySize; i++ )
        {
            var _Element = m_SequenceItems.GetArrayElementAtIndex( i );
            UpdateNameWithIndex( _Element, i );
        }
    }

    /// <summary>
    /// 繪製序列項目
    /// </summary>
    private void DrawSequenceItems()
    {
        EditorGUILayout.Space( 5 );
        m_ReorderableList.DoLayoutList();
        EditorGUILayout.Space( 5 );
    }

    /// <summary>
    /// 繪製序列項目元素（使用 EditorGUI 排版，不能用 GUILayout/EditorGUILayout）
    /// </summary>
    /// <param name="iRect">繪製區域</param>
    /// <param name="iIndex">項目索引</param>
    /// <param name="iIsActive">是否為活動項目</param>
    /// <param name="iIsFocused">是否為焦點項目</param>
    private void DrawSequenceItemElement( Rect iRect, int iIndex, bool iIsActive, bool iIsFocused )
    {
        var _Element = m_ReorderableList.serializedProperty.GetArrayElementAtIndex( iIndex );

        // 取得序列化屬性
        var _Name = _Element.FindPropertyRelative( "m_Name" );
        var _ItemType = _Element.FindPropertyRelative( "m_ItemType" );
        var _IsEnabled = _Element.FindPropertyRelative( "m_IsEnabled" );
        var _DelayTime = _Element.FindPropertyRelative( "m_DelayTime" );
        var _LeanTweenVisual = _Element.FindPropertyRelative( "m_LeanTweenVisual" );
        var _WaitForCompletion = _Element.FindPropertyRelative( "m_WaitForCompletion" );

        // 常數定義
        const float ENABLED_WIDTH = 16f;     // 啟用勾選框寬度
        const float INDENT_WIDTH = 15f;      // 縮排寬度

        float x = iRect.x;
        float y = iRect.y;
        float w = iRect.width;
        float lineH = EditorGUIUtility.singleLineHeight;
        float spacing = 5f;

        // 設定標籤寬度
        float originalLabelWidth = EditorGUIUtility.labelWidth;

        y += spacing * 2;

        //編號
        float indexLabelX = x + ENABLED_WIDTH;
        Rect indexRect = new Rect( indexLabelX, y, 30, lineH );
        EditorGUI.LabelField( indexRect, $"[{iIndex + 1}]" );

        y += spacing * 5;

        //縮排
        float contentX = x + INDENT_WIDTH;
        float contentWidth = w - INDENT_WIDTH;

        //第一行：啟用勾選框
        EditorGUIUtility.labelWidth = 50;  // 調整標籤寬度
        Rect enabledRect = new Rect( contentX, y, contentWidth, lineH );
        EditorGUI.PropertyField( enabledRect, _IsEnabled, new GUIContent( "是否啟用" ) );

        y += spacing * 3;  // 基本設定區域的頂部間距
        
        //第二行：名稱
        Rect nameRect = new Rect( contentX, y, contentWidth, 45 );
        EditorGUI.PropertyField( nameRect, _Name, new GUIContent( "名稱" ) );
        y += 45 + spacing * 2;

        // 第三行：類型
        Rect typeRect = new Rect( contentX, y, contentWidth, lineH );
        EditorGUI.PropertyField( typeRect, _ItemType, new GUIContent( "類型" ) );
        y += lineH + spacing * 2;

        // 第四行：延遲時間
        Rect delayRect = new Rect( contentX, y, contentWidth, lineH );
        EditorGUI.PropertyField( delayRect, _DelayTime, new GUIContent( "延遲 (秒)" ) );
        y += lineH + spacing / 2;  // 基本設定區域的底部間距

        // LeanTweenVisual 相關設定
        if( _ItemType.enumValueIndex == (int)UIAnimationScheduler.SequenceItemType.LeanTweenVisual )
        {
            EditorGUI.indentLevel++;
            float subContentX = contentX + INDENT_WIDTH;
            float subContentWidth = contentWidth - INDENT_WIDTH;

            // LeanTweenVisual 組件
            y += spacing * 2;  // LeanTweenVisual 區域的頂部間距
            
            EditorGUIUtility.labelWidth = 125;  // 調整標籤寬度以適應
            Rect visualRect = new Rect( subContentX, y, subContentWidth, 45 );
            EditorGUI.PropertyField( visualRect, _LeanTweenVisual, new GUIContent( "動畫組件" ) );
            y += 45 + spacing * 2;

            // 等待完成選項
            Rect waitRect = new Rect( subContentX, y, subContentWidth, lineH );
            EditorGUI.PropertyField( waitRect, _WaitForCompletion, new GUIContent( "等待此動畫播放完成" ) );
            y += lineH + spacing * 2;

            EditorGUI.indentLevel--;
        }

        // 還原標籤寬度
        EditorGUIUtility.labelWidth = originalLabelWidth;
    }

    /// <summary>
    /// 計算序列項目元素的高度（根據欄位顯示內容）
    /// </summary>
    /// <param name="iIndex">項目索引</param>
    /// <returns>元素高度</returns>
    private float GetSequenceItemElementHeight( int iIndex )
    {
        var _Element = m_ReorderableList.serializedProperty.GetArrayElementAtIndex( iIndex );
        return CalculateElementHeight( _Element );
    }

    /// <summary>
    /// 計算序列項目元素的實際高度
    /// </summary>
    /// <param name="iElement">序列化的元素屬性</param>
    /// <returns>計算後的高度</returns>
    private float CalculateElementHeight( SerializedProperty iElement )
    {
        float _LineHeight = EditorGUIUtility.singleLineHeight;
        float spacing = 5f;
        float _Height = 0;

        // 編號和啟用勾選框區域
        _Height += _LineHeight;              // 標題列高度
        _Height += spacing * 3;              // 間距

        // 啟用勾選框
        _Height += _LineHeight;              // 啟用勾選框高度
        _Height += spacing * 3;              // 間距

        // 名稱（縮排）
        _Height += 45;                       // 名稱欄位高度
        _Height += spacing * 2;              // 間距

        // 類型
        _Height += _LineHeight;              // 類型下拉選單高度
        _Height += spacing * 2;              // 間距

        // 延遲時間
        _Height += _LineHeight;              // 延遲時間欄位高度
        _Height += spacing / 2;              // 較小的底部間距

        // 檢查是否為 LeanTweenVisual 類型
        var _ItemType = iElement.FindPropertyRelative( "m_ItemType" );
        if( _ItemType.enumValueIndex == (int)UIAnimationScheduler.SequenceItemType.LeanTweenVisual )
        {
            _Height += spacing * 2;          // LeanTweenVisual 區域的頂部間距

            _Height += 45;                   // 動畫組件高度
            _Height += spacing * 2;          // 間距

            _Height += _LineHeight;          // 等待完成選項
            _Height += spacing * 2;          // 間距
        }

        return _Height;
    }
    #endregion
}
