---=====================================================================
---              CHINESE GAMER PROPRIETARY INFORMATION
---
--- This software is supplied under the terms of a license agreement or
--- nondisclosure agreement with CHINESE GAMER and may not
--- be copied or disclosed except in accordance with the terms of that
--- agreement.
---
---                 Copyright © 2022 by CHINESE GAMER.
---                      All Rights Reserved.
---
---    -------------------------------------------------------------
---
---=====================================================================

---@class SkillActData
---author
---version 1.0
---since [ProjectBase] 0.1
---date 2022.4.14

SkillActData = {}
SkillActData.__index = SkillActData

-- local json = require "cjson"

local this = SkillActData

--- 此串檔是否已經初始化完成
this.m_IsDataInitialized = false

local ASSET_NAME = "SkillActData"
local m_Dic = {}

SkillActData.iKind = 
{
    Man = "Male",
    Woman = "Female",
    Npc = "Npc"
}

---初始化
function SkillActData.Init()
    this.m_IsDataInitialized = true
    DataMgr.AddInitializedDataCount()
    return JsonMgr.LoadTableFromResources(ASSET_NAME, SkillActData.OnLoadData)
end

function SkillActData.OnLoadData(iFile)
    m_Dic = iFile
end

---取得SkillActData
---@param iKind SkillActData.iKind 讀取種類
---@param iSkillActIdx number iSkillAct編號
---@return SkillActData 演武資料
function SkillActData.GetSkillActDataByIdx(iKind, iIdx)
    local _Result = nil
    if iIdx > 0 then
        _Result = m_Dic[iKind][tostring(iIdx)]
    end

    if _Result == nil then
        D.Log("Cant Find SkillActData m_Idx: " .. iIdx)
    end

    return _Result
end

--- 取全部表 (寫給 editor 工具用的)
function SkillActData.GetAllData()
	return m_Dic
end

--- 暫存的已分類技能動作資料
SkillActData.m_TempClassifiedActData = {}

--- 將技能動作資料依照動作序列( ActionSequence )分類
function SkillActData.ClassifyActDataBySequence(iSkillActData)
    local _ClassifiedActDataInfo = {}

    -- 檢查是否已經分類過
    if SkillActData.m_TempClassifiedActData[iSkillActData.m_SkillActID] and BattleMgr.m_IS_BATTLEEDITOR_DEBUG == false then
        -- 如果已經分類過，則直接返回已分類的結果
        return SkillActData.m_TempClassifiedActData[iSkillActData.m_SkillActID]
    else
        if SkillActData.m_TempClassifiedActData[iSkillActData.m_SkillActID] then
            -- 在戰編播放的情況下，清除之前的分類結果，動作才會改變
            table.Clear(SkillActData.m_TempClassifiedActData[iSkillActData.m_SkillActID])
        else
            -- 如果沒有分類過，則進行分類
            -- 初始化分類表
            SkillActData.m_TempClassifiedActData[iSkillActData.m_SkillActID] = {}
        end
        -- 遍歷 s_ActData
        for s_ActDataIdx, _ActData in ipairs(iSkillActData.s_ActData) do
            local _tempInfo =
            {
                m_s_ActDataIdx = s_ActDataIdx,
                m_ActionSequence = _ActData.m_ActionSequence,
                m_ConditionalTickID = _ActData.m_ConditionalTickID
            }

            -- 如果分類表中尚未存在該序列，則初始化
            if not _ClassifiedActDataInfo[_tempInfo.m_ActionSequence] then
                _ClassifiedActDataInfo[_tempInfo.m_ActionSequence] = {}
            end
            -- 將當前 _ActData 加入對應的分類
            table.insert(_ClassifiedActDataInfo[_tempInfo.m_ActionSequence], _tempInfo)
        end

        -- 將分類後的結果存入暫存表
        SkillActData.m_TempClassifiedActData[iSkillActData.m_SkillActID] = _ClassifiedActDataInfo

        -- 返回分類後的結果
        return _ClassifiedActDataInfo
    end
end